#!/bin/bash

echo "Starting ResuMatch application..."

# Check if Python is available
if command -v python3 &>/dev/null; then
    PYTHON_CMD="python3"
elif command -v python &>/dev/null; then
    PYTHON_CMD="python"
else
    echo "Error: Python not found. Please install Python 3."
    exit 1
fi

# Check if virtual environment exists
if [ -d ".venv" ]; then
    echo "Activating virtual environment..."
    source .venv/bin/activate
fi

# Run the application
echo "Running the application with $PYTHON_CMD..."
$PYTHON_CMD app.py

# Check if the application started successfully
if [ $? -ne 0 ]; then
    echo "Error: Failed to start the application."
    echo "Please make sure all dependencies are installed:"
    echo "pip install -r requirements.txt"
    exit 1
fi
