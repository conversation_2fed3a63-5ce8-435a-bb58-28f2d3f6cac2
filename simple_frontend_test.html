<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResuMatch - Simple Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .job-match {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .match-score {
            font-weight: bold;
            color: #28a745;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ResuMatch - AI Resume Matching System</h1>
        
        <div class="section">
            <h2>📋 Available Jobs</h2>
            <button onclick="loadJobs()">Load Available Jobs</button>
            <div id="jobsResults"></div>
        </div>
        
        <div class="section">
            <h2>📄 Upload Resume</h2>
            <input type="file" id="resumeFile" accept=".pdf" />
            <button onclick="uploadResume()">Upload Resume</button>
            <div id="uploadResults"></div>
        </div>
        
        <div class="section">
            <h2>🎯 Find Job Matches</h2>
            <p>Upload a resume first, then click to find matches:</p>
            <button id="matchButton" onclick="findMatches()" disabled>Find Job Matches</button>
            <div id="matchResults"></div>
        </div>
        
        <div class="section">
            <h2>🔗 API Documentation</h2>
            <p>For detailed API documentation, visit: <a href="http://127.0.0.1:8000/docs" target="_blank">http://127.0.0.1:8000/docs</a></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000';
        let currentResumeId = null;

        async function loadJobs() {
            const resultsDiv = document.getElementById('jobsResults');
            resultsDiv.innerHTML = '<div class="loading">Loading jobs...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/api/jobs`);
                const jobs = await response.json();
                
                if (jobs.length === 0) {
                    resultsDiv.innerHTML = '<div class="error">No jobs found. Please create some jobs first.</div>';
                    return;
                }
                
                let html = '<div class="results"><h3>Available Jobs:</h3>';
                jobs.forEach((job, index) => {
                    html += `
                        <div class="job-match">
                            <strong>${job.title}</strong> at <em>${job.company}</em><br>
                            <small>Job ID: ${job.job_id}</small><br>
                            <small>Location: ${job.location || 'Not specified'}</small>
                        </div>
                    `;
                });
                html += '</div>';
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error loading jobs: ${error.message}</div>`;
            }
        }

        async function uploadResume() {
            const fileInput = document.getElementById('resumeFile');
            const resultsDiv = document.getElementById('uploadResults');
            
            if (!fileInput.files[0]) {
                resultsDiv.innerHTML = '<div class="error">Please select a PDF file first.</div>';
                return;
            }
            
            resultsDiv.innerHTML = '<div class="loading">Uploading and processing resume...</div>';
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('name', fileInput.files[0].name);
            
            try {
                const response = await fetch(`${API_BASE}/api/documents/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    currentResumeId = result.resume_id;
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ Resume uploaded successfully!<br>
                            Resume ID: ${result.resume_id}
                        </div>
                    `;
                    document.getElementById('matchButton').disabled = false;
                } else {
                    resultsDiv.innerHTML = `<div class="error">Upload failed: ${result.detail || 'Unknown error'}</div>`;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error uploading resume: ${error.message}</div>`;
            }
        }

        async function findMatches() {
            if (!currentResumeId) {
                document.getElementById('matchResults').innerHTML = '<div class="error">Please upload a resume first.</div>';
                return;
            }
            
            const resultsDiv = document.getElementById('matchResults');
            resultsDiv.innerHTML = '<div class="loading">Finding job matches...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/api/documents/match?resume_id=${currentResumeId}&threshold=0.5&limit=10`);
                const result = await response.json();
                
                if (response.ok) {
                    const matches = result.matches;
                    
                    if (matches.length === 0) {
                        resultsDiv.innerHTML = '<div class="error">No job matches found. Try lowering the threshold.</div>';
                        return;
                    }
                    
                    let html = '<div class="results"><h3>Job Matches Found:</h3>';
                    matches.forEach((match, index) => {
                        const percentage = (match.match_score * 100).toFixed(1);
                        const scoreColor = match.match_score >= 0.8 ? '#28a745' : 
                                         match.match_score >= 0.7 ? '#ffc107' : '#17a2b8';
                        
                        html += `
                            <div class="job-match">
                                <strong>${match.title}</strong> at <em>${match.company}</em><br>
                                <span class="match-score" style="color: ${scoreColor}">
                                    Match Score: ${match.match_score.toFixed(2)} (${percentage}%)
                                </span><br>
                                <small>Job ID: ${match.job_id}</small>
                            </div>
                        `;
                    });
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                    
                } else {
                    resultsDiv.innerHTML = `<div class="error">Error finding matches: ${result.detail || 'Unknown error'}</div>`;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error finding matches: ${error.message}</div>`;
            }
        }

        // Load jobs on page load
        window.onload = function() {
            loadJobs();
        };
    </script>
</body>
</html>
