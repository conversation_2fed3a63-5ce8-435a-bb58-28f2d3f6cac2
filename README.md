# ResuMatch

ResuMatch is an AI-powered resume to job matching application that uses advanced natural language processing and machine learning techniques to analyze resumes and job descriptions, providing a match score and recommendations.

## Features

- Upload resume in PDF, DOCX, or TXT format
- Enter job descriptions to match against
- Get a match score and quality assessment
- View extracted skills, experience, and education from your resume
- Receive tailored recommendations based on match quality

## Technology Stack

- **Backend**: FastAPI
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **ML/AI**: PyTorch, spaCy, Siamese Neural Networks
- **Document Processing**: pdfminer.six, pytesseract, python-docx

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/resumatch.git
   cd resumatch
   ```

2. Create a virtual environment and activate it:
   ```
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

4. Download the spaCy model:
   ```
   python -m spacy download en_core_web_lg
   ```

## Running the Application

1. Start the FastAPI server:
   ```
   python app.py
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:8000
   ```

## Project Structure

- `app.py`: Main FastAPI application
- `resumatch/`: Core package containing all the components
  - `embeddings/`: Text embedding modules
  - `models/`: Neural network architectures
  - `ocr/`: Document processing utilities
  - `ner/`: Named entity recognition components
  - `siamese_network/`: Siamese network implementation
- `templates/`: HTML templates for the web interface
- `static/`: Static assets (CSS, JS)
- `data/`: Data storage
  - `uploads/`: Temporary storage for uploaded resumes
  - `datasets/`: Training and evaluation datasets

## How It Works

1. **Document Processing**: Extracts text from uploaded resumes using OCR and text extraction tools
2. **Entity Extraction**: Identifies key information like skills, experience, and education
3. **Text Embedding**: Converts text into numerical vectors using advanced NLP techniques
4. **Similarity Calculation**: Uses a trained Siamese neural network to calculate the similarity between resume and job description
5. **Scoring and Recommendations**: Provides a match score and tailored recommendations

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- spaCy for NLP capabilities
- PyTorch for neural network implementation
- FastAPI for the web framework
