"""
Data Augmentation for ResuMatch

This module provides advanced data augmentation techniques for the ResuMatch system
to improve model performance by generating more diverse and realistic training data.
"""

import numpy as np
from typing import Dict, List, Tuple, Any
import random
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize

class DataAugmenter:
    """
    Class for augmenting resume and job data to improve model training.
    """

    def __init__(self, random_seed: int = 42):
        """
        Initialize the data augmenter

        Args:
            random_seed (int): Random seed for reproducibility
        """
        self.random_seed = random_seed
        np.random.seed(random_seed)
        random.seed(random_seed)

    def generate_synthetic_embeddings(self,
                                      embeddings: Dict[str, np.ndarray],
                                      num_synthetic: int = 10,
                                      noise_level: float = 0.01,
                                      use_pca: bool = True) -> Dict[str, np.ndarray]:
        """
        Generate synthetic embeddings by adding controlled noise to existing embeddings

        Args:
            embeddings (Dict[str, np.ndarray]): Original embeddings
            num_synthetic (int): Number of synthetic embeddings to generate per original
            noise_level (float): Level of noise to add (as a fraction of embedding magnitude)
            use_pca (bool): Whether to use PCA for more realistic noise generation

        Returns:
            Dict[str, np.ndarray]: Synthetic embeddings
        """
        synthetic_embeddings = {}

        # Extract all embeddings as a matrix for PCA
        if use_pca and len(embeddings) > 1:
            # Flatten embeddings if they are multi-dimensional
            flattened_embeddings = []
            for emb in embeddings.values():
                flattened_embeddings.append(emb.flatten())

            all_embeddings = np.stack(flattened_embeddings)

            # Determine number of components (min of embedding dim and num samples)
            n_components = min(all_embeddings.shape[0] - 1, all_embeddings.shape[1])
            n_components = max(n_components, 1)  # Ensure at least 1 component

            # Fit PCA
            pca = PCA(n_components=n_components)
            pca.fit(all_embeddings)

            # Generate synthetic embeddings using PCA components
            for embedding_id, embedding in embeddings.items():
                embedding_flat = embedding.flatten()
                embedding_shape = embedding.shape

                for i in range(num_synthetic):
                    # Generate noise along principal components
                    noise = np.zeros_like(embedding_flat)
                    for j, (component, variance) in enumerate(zip(pca.components_, pca.explained_variance_)):
                        noise += np.random.normal(0, np.sqrt(variance) * noise_level, 1) * component

                    # Add noise to original embedding
                    synthetic_embedding_flat = embedding_flat + noise

                    # Normalize to maintain unit length
                    synthetic_embedding_flat = normalize(synthetic_embedding_flat.reshape(1, -1))[0]

                    # Reshape back to original shape
                    synthetic_embedding = synthetic_embedding_flat.reshape(embedding_shape)

                    synthetic_id = f"{embedding_id}_syn_{i}"
                    synthetic_embeddings[synthetic_id] = synthetic_embedding
        else:
            # Simple noise addition if PCA is not used or not enough samples
            for embedding_id, embedding in embeddings.items():
                embedding_shape = embedding.shape

                for i in range(num_synthetic):
                    # Add Gaussian noise
                    noise = np.random.normal(0, noise_level, embedding.shape)
                    synthetic_embedding = embedding + noise

                    # Normalize to maintain unit length
                    synthetic_embedding_flat = synthetic_embedding.flatten()
                    synthetic_embedding_flat = normalize(synthetic_embedding_flat.reshape(1, -1))[0]

                    # Reshape back to original shape
                    synthetic_embedding = synthetic_embedding_flat.reshape(embedding_shape)

                    synthetic_id = f"{embedding_id}_syn_{i}"
                    synthetic_embeddings[synthetic_id] = synthetic_embedding

        return synthetic_embeddings

    def generate_mixup_embeddings(self,
                                 embeddings: Dict[str, np.ndarray],
                                 num_mixups: int = 5) -> Dict[str, np.ndarray]:
        """
        Generate synthetic embeddings by mixing up existing embeddings

        Args:
            embeddings (Dict[str, np.ndarray]): Original embeddings
            num_mixups (int): Number of mixup embeddings to generate

        Returns:
            Dict[str, np.ndarray]: Mixup embeddings
        """
        if len(embeddings) < 2:
            return {}  # Need at least 2 embeddings for mixup

        mixup_embeddings = {}
        embedding_ids = list(embeddings.keys())

        for i in range(num_mixups):
            # Randomly select two embeddings
            id1, id2 = random.sample(embedding_ids, 2)
            embedding1 = embeddings[id1]
            embedding2 = embeddings[id2]

            # Ensure embeddings have the same shape
            if embedding1.shape != embedding2.shape:
                # Flatten both embeddings
                embedding1_flat = embedding1.flatten()
                embedding2_flat = embedding2.flatten()

                # Use the smaller dimension
                min_dim = min(len(embedding1_flat), len(embedding2_flat))
                embedding1_flat = embedding1_flat[:min_dim]
                embedding2_flat = embedding2_flat[:min_dim]

                # Random mixup ratio
                alpha = np.random.beta(0.4, 0.4)  # Beta distribution for mixup ratio

                # Mix embeddings
                mixed_embedding = alpha * embedding1_flat + (1 - alpha) * embedding2_flat

                # Normalize
                mixed_embedding = normalize(mixed_embedding.reshape(1, -1))[0]

                # Reshape to match the first embedding's shape
                mixed_embedding = mixed_embedding.reshape(embedding1.shape)
            else:
                # Random mixup ratio
                alpha = np.random.beta(0.4, 0.4)  # Beta distribution for mixup ratio

                # Mix embeddings
                mixed_embedding = alpha * embedding1 + (1 - alpha) * embedding2

                # Normalize
                mixed_embedding_flat = mixed_embedding.flatten()
                mixed_embedding_flat = normalize(mixed_embedding_flat.reshape(1, -1))[0]
                mixed_embedding = mixed_embedding_flat.reshape(embedding1.shape)

            mixup_id = f"mixup_{i}_{id1}_{id2}"
            mixup_embeddings[mixup_id] = mixed_embedding

        return mixup_embeddings

    def generate_hard_negative_pairs(self,
                                    resume_embeddings: Dict[str, np.ndarray],
                                    job_embeddings: Dict[str, np.ndarray],
                                    positive_pairs: List[Tuple[str, str]],
                                    num_hard_negatives: int = 3) -> List[Tuple[str, str]]:
        """
        Generate hard negative pairs (resume-job pairs that are similar but not matches)

        Args:
            resume_embeddings (Dict[str, np.ndarray]): Resume embeddings
            job_embeddings (Dict[str, np.ndarray]): Job embeddings
            positive_pairs (List[Tuple[str, str]]): Positive pairs to avoid
            num_hard_negatives (int): Number of hard negatives per resume

        Returns:
            List[Tuple[str, str]]: Hard negative pairs
        """
        hard_negative_pairs = []

        # For each resume
        for resume_id, resume_embedding in resume_embeddings.items():
            # Calculate similarity to all jobs
            similarities = []
            for job_id, job_embedding in job_embeddings.items():
                # Skip if this is a positive pair
                if (resume_id, job_id) in positive_pairs:
                    continue

                # Calculate cosine similarity
                resume_flat = resume_embedding.flatten()
                job_flat = job_embedding.flatten()

                # Use the smaller dimension if they differ
                if len(resume_flat) != len(job_flat):
                    min_dim = min(len(resume_flat), len(job_flat))
                    resume_flat = resume_flat[:min_dim]
                    job_flat = job_flat[:min_dim]

                # Calculate cosine similarity
                similarity = np.dot(resume_flat, job_flat) / (
                    np.linalg.norm(resume_flat) * np.linalg.norm(job_flat) + 1e-8  # Add small epsilon to avoid division by zero
                )
                similarities.append((job_id, similarity))

            # Sort by similarity (highest first)
            similarities.sort(key=lambda x: x[1], reverse=True)

            # Take top N as hard negatives
            top_n = min(num_hard_negatives, len(similarities))
            for i in range(top_n):
                if i < len(similarities):
                    job_id, _ = similarities[i]
                    hard_negative_pairs.append((resume_id, job_id))

        return hard_negative_pairs

    def generate_balanced_dataset(self,
                                 resume_embeddings: Dict[str, np.ndarray],
                                 job_embeddings: Dict[str, np.ndarray],
                                 num_synthetic_resumes: int = 20,
                                 num_synthetic_jobs: int = 10,
                                 num_mixups: int = 5,
                                 num_positives_per_resume: int = 5,
                                 num_hard_negatives_per_resume: int = 3,
                                 num_random_negatives_per_resume: int = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Generate a balanced dataset with advanced augmentation techniques

        Args:
            resume_embeddings (Dict[str, np.ndarray]): Original resume embeddings
            job_embeddings (Dict[str, np.ndarray]): Original job embeddings
            num_synthetic_resumes (int): Number of synthetic resumes per original
            num_synthetic_jobs (int): Number of synthetic jobs per original
            num_mixups (int): Number of mixup embeddings to generate
            num_positives_per_resume (int): Number of positive pairs per resume
            num_hard_negatives_per_resume (int): Number of hard negative pairs per resume
            num_random_negatives_per_resume (int): Number of random negative pairs per resume

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: Resume vectors, job vectors, labels
        """
        # Step 1: Generate synthetic embeddings
        synthetic_resume_embeddings = self.generate_synthetic_embeddings(
            resume_embeddings, num_synthetic=num_synthetic_resumes, use_pca=True
        )
        synthetic_job_embeddings = self.generate_synthetic_embeddings(
            job_embeddings, num_synthetic=num_synthetic_jobs, use_pca=True
        )

        # Step 2: Generate mixup embeddings
        mixup_resume_embeddings = self.generate_mixup_embeddings(
            resume_embeddings, num_mixups=num_mixups
        )
        mixup_job_embeddings = self.generate_mixup_embeddings(
            job_embeddings, num_mixups=num_mixups
        )

        # Step 3: Combine all embeddings
        all_resume_embeddings = {**resume_embeddings, **synthetic_resume_embeddings, **mixup_resume_embeddings}
        all_job_embeddings = {**job_embeddings, **synthetic_job_embeddings, **mixup_job_embeddings}

        print(f"Created {len(all_resume_embeddings) - len(resume_embeddings)} augmented resume embeddings")
        print(f"Created {len(all_job_embeddings) - len(job_embeddings)} augmented job embeddings")

        # Step 4: Generate positive pairs based on semantic similarity
        positive_pairs = []
        all_resume_ids = list(all_resume_embeddings.keys())
        all_job_ids = list(all_job_embeddings.keys())

        # Calculate similarity between all resume-job pairs to find natural matches
        for resume_id in all_resume_ids:
            resume_embedding = all_resume_embeddings[resume_id]

            # Find the most similar jobs for this resume
            similarities = []
            for job_id in all_job_ids:
                job_embedding = all_job_embeddings[job_id]
                # Calculate cosine similarity
                resume_flat = resume_embedding.flatten()
                job_flat = job_embedding.flatten()

                # Use the smaller dimension if they differ
                if len(resume_flat) != len(job_flat):
                    min_dim = min(len(resume_flat), len(job_flat))
                    resume_flat = resume_flat[:min_dim]
                    job_flat = job_flat[:min_dim]

                # Calculate cosine similarity
                similarity = np.dot(resume_flat, job_flat) / (
                    np.linalg.norm(resume_flat) * np.linalg.norm(job_flat) + 1e-8  # Add small epsilon to avoid division by zero
                )
                similarities.append((job_id, similarity))

            # Sort by similarity (highest first)
            similarities.sort(key=lambda x: x[1], reverse=True)

            # Take top N as positive examples
            top_n = min(num_positives_per_resume, len(similarities))
            for i in range(top_n):
                if i < len(similarities):
                    job_id, _ = similarities[i]
                    positive_pairs.append((resume_id, job_id))

        # Step 5: Generate hard negative pairs
        hard_negative_pairs = self.generate_hard_negative_pairs(
            all_resume_embeddings, all_job_embeddings, positive_pairs, num_hard_negatives_per_resume
        )

        # Step 6: Generate random negative pairs
        random_negative_pairs = []
        for resume_id in all_resume_ids:
            # Get a list of jobs that are not in positive or hard negative pairs
            existing_pairs = positive_pairs + hard_negative_pairs
            negative_job_ids = [job_id for job_id in all_job_ids
                               if (resume_id, job_id) not in existing_pairs]

            # Randomly select negative examples
            num_negatives = min(len(negative_job_ids), num_random_negatives_per_resume)
            if num_negatives > 0:
                selected_job_ids = random.sample(negative_job_ids, num_negatives)
                for job_id in selected_job_ids:
                    random_negative_pairs.append((resume_id, job_id))

        # Step 7: Create the final balanced dataset
        negative_pairs = hard_negative_pairs + random_negative_pairs
        print(f"Generated {len(positive_pairs)} positive pairs and {len(negative_pairs)} negative pairs")

        # Create vectors and labels
        resume_vectors = []
        job_vectors = []
        labels = []

        # Add positive pairs
        for resume_id, job_id in positive_pairs:
            resume_vectors.append(all_resume_embeddings[resume_id])
            job_vectors.append(all_job_embeddings[job_id])
            labels.append(1)

        # Add negative pairs
        for resume_id, job_id in negative_pairs:
            resume_vectors.append(all_resume_embeddings[resume_id])
            job_vectors.append(all_job_embeddings[job_id])
            labels.append(0)

        # Convert to numpy arrays
        resume_vectors = np.array(resume_vectors)
        job_vectors = np.array(job_vectors)
        labels = np.array(labels)

        # Shuffle the data
        indices = np.arange(len(labels))
        np.random.shuffle(indices)
        resume_vectors = resume_vectors[indices]
        job_vectors = job_vectors[indices]
        labels = labels[indices]

        return resume_vectors, job_vectors, labels
