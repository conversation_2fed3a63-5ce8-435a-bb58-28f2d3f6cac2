"""
Dataset Enhancer for ResuMatch

This module provides functions to enhance resume and job datasets with additional features
and improve the quality of the data for better matching.
"""

import os
import json
import random
import logging
import numpy as np
from typing import Dict, List, Any, Tuple
from pathlib import Path
import spacy
from tqdm import tqdm

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/dataset_enhancer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("dataset-enhancer")

# Load spaCy model for text processing
try:
    nlp = spacy.load("en_core_web_lg")
    logger.info("Loaded spaCy model: en_core_web_lg")
except OSError:
    logger.warning("spaCy model not found. Downloading en_core_web_lg...")
    import subprocess
    subprocess.call(["python", "-m", "spacy", "download", "en_core_web_lg"])
    nlp = spacy.load("en_core_web_lg")
    logger.info("Downloaded and loaded spaCy model: en_core_web_lg")

class DatasetEnhancer:
    """
    Class for enhancing resume and job datasets with additional features.
    """
    
    def __init__(self, data_dir: str = None):
        """
        Initialize the dataset enhancer
        
        Args:
            data_dir (str): Path to the data directory
        """
        if data_dir is None:
            # Use default data directory
            base_path = Path(__file__).resolve().parent.parent.parent
            self.data_dir = os.path.join(base_path, "data")
        else:
            self.data_dir = data_dir
            
        # Create directories if they don't exist
        os.makedirs(os.path.join(self.data_dir, "enhanced"), exist_ok=True)
        
        # Initialize skill taxonomy
        self._initialize_skill_taxonomy()
        
    def _initialize_skill_taxonomy(self):
        """Initialize the skill taxonomy for better skill categorization"""
        # Technical skills by category
        self.skill_taxonomy = {
            "programming_languages": [
                "python", "java", "javascript", "typescript", "c++", "c#", "ruby", "php", "swift",
                "kotlin", "go", "rust", "scala", "perl", "r", "matlab", "sql", "bash", "powershell"
            ],
            "web_development": [
                "html", "css", "sass", "less", "bootstrap", "tailwind", "material-ui", "react",
                "angular", "vue", "svelte", "jquery", "node.js", "express", "django", "flask",
                "spring", "asp.net", "laravel", "ruby on rails"
            ],
            "data_science": [
                "tensorflow", "pytorch", "keras", "scikit-learn", "pandas", "numpy", "scipy",
                "matplotlib", "seaborn", "d3.js", "tableau", "power bi", "machine learning",
                "deep learning", "natural language processing", "computer vision", "statistics"
            ],
            "databases": [
                "sql", "nosql", "mongodb", "postgresql", "mysql", "oracle", "sqlite", "redis",
                "cassandra", "dynamodb", "firebase", "elasticsearch"
            ],
            "devops": [
                "aws", "azure", "gcp", "docker", "kubernetes", "jenkins", "travis ci", "circle ci",
                "git", "github", "gitlab", "bitbucket", "jira", "confluence", "terraform", "ansible"
            ],
            "soft_skills": [
                "communication", "teamwork", "leadership", "problem solving", "critical thinking",
                "creativity", "time management", "organization", "adaptability", "flexibility"
            ]
        }
        
        # Flatten the taxonomy for easy lookup
        self.all_skills = []
        self.skill_to_category = {}
        for category, skills in self.skill_taxonomy.items():
            self.all_skills.extend(skills)
            for skill in skills:
                self.skill_to_category[skill] = category
    
    def enhance_resume_dataset(self, input_file: str, output_file: str = None) -> str:
        """
        Enhance a resume dataset with additional features
        
        Args:
            input_file (str): Path to the input resume dataset JSON file
            output_file (str): Path to the output enhanced dataset JSON file
            
        Returns:
            str: Path to the enhanced dataset file
        """
        logger.info(f"Enhancing resume dataset: {input_file}")
        
        # Set default output file if not provided
        if output_file is None:
            output_dir = os.path.join(self.data_dir, "enhanced")
            output_file = os.path.join(output_dir, "enhanced_resumes.json")
        
        # Load the input dataset
        try:
            with open(input_file, 'r') as f:
                resumes = json.load(f)
            logger.info(f"Loaded {len(resumes)} resumes from {input_file}")
        except Exception as e:
            logger.error(f"Error loading resume dataset: {e}")
            return None
        
        # Enhance each resume
        enhanced_resumes = []
        for resume in tqdm(resumes, desc="Enhancing resumes"):
            enhanced_resume = self._enhance_resume(resume)
            enhanced_resumes.append(enhanced_resume)
        
        # Save the enhanced dataset
        try:
            with open(output_file, 'w') as f:
                json.dump(enhanced_resumes, f, indent=2)
            logger.info(f"Saved {len(enhanced_resumes)} enhanced resumes to {output_file}")
        except Exception as e:
            logger.error(f"Error saving enhanced resume dataset: {e}")
            return None
        
        return output_file
    
    def enhance_job_dataset(self, input_file: str, output_file: str = None) -> str:
        """
        Enhance a job dataset with additional features
        
        Args:
            input_file (str): Path to the input job dataset JSON file
            output_file (str): Path to the output enhanced dataset JSON file
            
        Returns:
            str: Path to the enhanced dataset file
        """
        logger.info(f"Enhancing job dataset: {input_file}")
        
        # Set default output file if not provided
        if output_file is None:
            output_dir = os.path.join(self.data_dir, "enhanced")
            output_file = os.path.join(output_dir, "enhanced_jobs.json")
        
        # Load the input dataset
        try:
            with open(input_file, 'r') as f:
                jobs = json.load(f)
            logger.info(f"Loaded {len(jobs)} jobs from {input_file}")
        except Exception as e:
            logger.error(f"Error loading job dataset: {e}")
            return None
        
        # Enhance each job
        enhanced_jobs = []
        for job in tqdm(jobs, desc="Enhancing jobs"):
            enhanced_job = self._enhance_job(job)
            enhanced_jobs.append(enhanced_job)
        
        # Save the enhanced dataset
        try:
            with open(output_file, 'w') as f:
                json.dump(enhanced_jobs, f, indent=2)
            logger.info(f"Saved {len(enhanced_jobs)} enhanced jobs to {output_file}")
        except Exception as e:
            logger.error(f"Error saving enhanced job dataset: {e}")
            return None
        
        return output_file
    
    def _enhance_resume(self, resume: Dict) -> Dict:
        """
        Enhance a single resume with additional features
        
        Args:
            resume (dict): Resume data
            
        Returns:
            dict: Enhanced resume data
        """
        # Create a copy of the resume to avoid modifying the original
        enhanced = resume.copy()
        
        # Add skill categories if skills are present
        if 'entities' in enhanced and 'skills' in enhanced['entities']:
            skill_categories = self._categorize_skills(enhanced['entities']['skills'])
            enhanced['skill_categories'] = skill_categories
        
        # Add experience level based on work experience
        if 'entities' in enhanced and 'experience' in enhanced['entities']:
            experience_level = self._determine_experience_level(enhanced['entities']['experience'])
            enhanced['experience_level'] = experience_level
        
        # Add education level if education is present
        if 'entities' in enhanced and 'education' in enhanced['entities']:
            education_level = self._determine_education_level(enhanced['entities']['education'])
            enhanced['education_level'] = education_level
        
        # Add resume quality score
        enhanced['quality_score'] = self._calculate_resume_quality(enhanced)
        
        return enhanced
    
    def _enhance_job(self, job: Dict) -> Dict:
        """
        Enhance a single job with additional features
        
        Args:
            job (dict): Job data
            
        Returns:
            dict: Enhanced job data
        """
        # Create a copy of the job to avoid modifying the original
        enhanced = job.copy()
        
        # Extract skills from requirements if present
        if 'requirements' in enhanced:
            skills = self._extract_skills_from_text(enhanced['requirements'])
            if 'skills' not in enhanced:
                enhanced['skills'] = skills
            
            # Add skill categories
            skill_categories = self._categorize_skills(skills)
            enhanced['skill_categories'] = skill_categories
        
        # Extract required experience level
        if 'requirements' in enhanced:
            experience_level = self._extract_experience_requirement(enhanced['requirements'])
            enhanced['required_experience'] = experience_level
        
        # Extract required education level
        if 'requirements' in enhanced:
            education_level = self._extract_education_requirement(enhanced['requirements'])
            enhanced['required_education'] = education_level
        
        # Add job complexity score
        enhanced['complexity_score'] = self._calculate_job_complexity(enhanced)
        
        return enhanced
    
    def _categorize_skills(self, skills: List[str]) -> Dict[str, List[str]]:
        """
        Categorize skills into different categories
        
        Args:
            skills (list): List of skills
            
        Returns:
            dict: Dictionary mapping skill categories to lists of skills
        """
        categorized = {category: [] for category in self.skill_taxonomy.keys()}
        categorized["other"] = []
        
        for skill in skills:
            skill_lower = skill.lower()
            
            # Check if the skill is in our taxonomy
            found = False
            for category, category_skills in self.skill_taxonomy.items():
                if any(s in skill_lower for s in category_skills):
                    categorized[category].append(skill)
                    found = True
                    break
            
            if not found:
                categorized["other"].append(skill)
        
        # Remove empty categories
        return {k: v for k, v in categorized.items() if v}
    
    def _determine_experience_level(self, experience: List[str]) -> str:
        """
        Determine experience level based on work experience
        
        Args:
            experience (list): List of experience descriptions
            
        Returns:
            str: Experience level (entry, mid, senior, executive)
        """
        # Simple heuristic based on number of experiences
        if not experience:
            return "entry"
        
        num_experiences = len(experience)
        
        if num_experiences <= 1:
            return "entry"
        elif num_experiences <= 3:
            return "mid"
        elif num_experiences <= 5:
            return "senior"
        else:
            return "executive"
    
    def _determine_education_level(self, education: List[str]) -> str:
        """
        Determine education level based on education history
        
        Args:
            education (list): List of education descriptions
            
        Returns:
            str: Education level (high_school, associate, bachelor, master, doctorate)
        """
        if not education:
            return "unknown"
        
        # Join all education entries into a single string for easier searching
        edu_text = " ".join(education).lower()
        
        if "phd" in edu_text or "doctorate" in edu_text or "doctoral" in edu_text:
            return "doctorate"
        elif "master" in edu_text or "mba" in edu_text or "ms " in edu_text or "ma " in edu_text:
            return "master"
        elif "bachelor" in edu_text or "bs " in edu_text or "ba " in edu_text or "bsc" in edu_text:
            return "bachelor"
        elif "associate" in edu_text or "aa " in edu_text or "as " in edu_text:
            return "associate"
        elif "high school" in edu_text or "ged" in edu_text or "diploma" in edu_text:
            return "high_school"
        else:
            return "unknown"
    
    def _calculate_resume_quality(self, resume: Dict) -> float:
        """
        Calculate a quality score for a resume
        
        Args:
            resume (dict): Resume data
            
        Returns:
            float: Quality score between 0 and 1
        """
        score = 0.0
        total_points = 0
        
        # Check for presence of key sections
        if 'entities' in resume:
            entities = resume['entities']
            
            # Contact information
            if 'email' in entities and entities['email']:
                score += 1
            total_points += 1
            
            if 'phone' in entities and entities['phone']:
                score += 1
            total_points += 1
            
            # Skills
            if 'skills' in entities:
                skill_count = len(entities['skills'])
                if skill_count >= 10:
                    score += 3
                elif skill_count >= 5:
                    score += 2
                elif skill_count >= 1:
                    score += 1
            total_points += 3
            
            # Experience
            if 'experience' in entities:
                exp_count = len(entities['experience'])
                if exp_count >= 3:
                    score += 3
                elif exp_count >= 1:
                    score += 2
            total_points += 3
            
            # Education
            if 'education' in entities:
                edu_count = len(entities['education'])
                if edu_count >= 1:
                    score += 2
            total_points += 2
            
            # Languages
            if 'languages' in entities and entities['languages']:
                score += 1
            total_points += 1
        
        # Calculate final score (normalize to 0-1)
        if total_points > 0:
            return score / total_points
        else:
            return 0.0
    
    def _extract_skills_from_text(self, text: str) -> List[str]:
        """
        Extract skills from text using NLP and skill taxonomy
        
        Args:
            text (str): Text to extract skills from
            
        Returns:
            list: List of extracted skills
        """
        if not text:
            return []
        
        # Process text with spaCy
        doc = nlp(text)
        
        # Extract skills using our taxonomy
        skills = []
        text_lower = text.lower()
        
        for skill in self.all_skills:
            if skill in text_lower:
                skills.append(skill)
        
        # Extract additional skills using noun chunks
        for chunk in doc.noun_chunks:
            if len(chunk.text) > 3 and not chunk.text.lower() in skills:
                # Check if this chunk might be a skill
                if not any(token.is_stop for token in chunk) and not any(token.is_punct for token in chunk):
                    skills.append(chunk.text)
        
        return list(set(skills))
    
    def _extract_experience_requirement(self, requirements: str) -> str:
        """
        Extract required experience level from job requirements
        
        Args:
            requirements (str): Job requirements text
            
        Returns:
            str: Required experience level (entry, mid, senior, executive)
        """
        if not requirements:
            return "unknown"
        
        requirements_lower = requirements.lower()
        
        # Look for explicit experience requirements
        if "10+ years" in requirements_lower or "senior" in requirements_lower or "lead" in requirements_lower:
            return "senior"
        elif "5+ years" in requirements_lower or "5-10 years" in requirements_lower:
            return "mid"
        elif "2+ years" in requirements_lower or "2-5 years" in requirements_lower or "3+ years" in requirements_lower:
            return "mid"
        elif "1+ year" in requirements_lower or "1-2 years" in requirements_lower or "entry level" in requirements_lower:
            return "entry"
        elif "no experience" in requirements_lower or "junior" in requirements_lower:
            return "entry"
        else:
            return "unknown"
    
    def _extract_education_requirement(self, requirements: str) -> str:
        """
        Extract required education level from job requirements
        
        Args:
            requirements (str): Job requirements text
            
        Returns:
            str: Required education level (high_school, associate, bachelor, master, doctorate)
        """
        if not requirements:
            return "unknown"
        
        requirements_lower = requirements.lower()
        
        if "phd" in requirements_lower or "doctorate" in requirements_lower or "doctoral" in requirements_lower:
            return "doctorate"
        elif "master" in requirements_lower or "mba" in requirements_lower or "ms degree" in requirements_lower:
            return "master"
        elif "bachelor" in requirements_lower or "bs degree" in requirements_lower or "ba degree" in requirements_lower:
            return "bachelor"
        elif "associate" in requirements_lower or "aa degree" in requirements_lower:
            return "associate"
        elif "high school" in requirements_lower or "ged" in requirements_lower:
            return "high_school"
        else:
            return "unknown"
    
    def _calculate_job_complexity(self, job: Dict) -> float:
        """
        Calculate a complexity score for a job
        
        Args:
            job (dict): Job data
            
        Returns:
            float: Complexity score between 0 and 1
        """
        score = 0.0
        total_points = 0
        
        # Check required experience
        if 'required_experience' in job:
            if job['required_experience'] == "senior":
                score += 3
            elif job['required_experience'] == "mid":
                score += 2
            elif job['required_experience'] == "entry":
                score += 1
            total_points += 3
        
        # Check required education
        if 'required_education' in job:
            if job['required_education'] == "doctorate":
                score += 4
            elif job['required_education'] == "master":
                score += 3
            elif job['required_education'] == "bachelor":
                score += 2
            elif job['required_education'] == "associate":
                score += 1
            total_points += 4
        
        # Check number of skills required
        if 'skills' in job:
            skill_count = len(job['skills'])
            if skill_count >= 10:
                score += 3
            elif skill_count >= 5:
                score += 2
            elif skill_count >= 1:
                score += 1
            total_points += 3
        
        # Calculate final score (normalize to 0-1)
        if total_points > 0:
            return score / total_points
        else:
            return 0.0

def enhance_datasets():
    """
    Enhance both resume and job datasets
    """
    # Set up paths
    base_path = Path(__file__).resolve().parent.parent.parent
    data_dir = os.path.join(base_path, "data")
    
    # Create enhancer
    enhancer = DatasetEnhancer(data_dir)
    
    # Enhance resume dataset
    resume_input = os.path.join(data_dir, "processed_resumes.json")
    if not os.path.exists(resume_input):
        resume_input = os.path.join(data_dir, "datasets", "processed_resumes.json")
    
    if os.path.exists(resume_input):
        enhanced_resumes = enhancer.enhance_resume_dataset(resume_input)
        print(f"Enhanced resume dataset saved to: {enhanced_resumes}")
    else:
        print(f"Resume dataset not found at {resume_input}")
    
    # Enhance job dataset
    job_input = os.path.join(data_dir, "generated_jobs.json")
    if not os.path.exists(job_input):
        job_input = os.path.join(data_dir, "datasets", "generated_jobs.json")
    
    if os.path.exists(job_input):
        enhanced_jobs = enhancer.enhance_job_dataset(job_input)
        print(f"Enhanced job dataset saved to: {enhanced_jobs}")
    else:
        print(f"Job dataset not found at {job_input}")

if __name__ == "__main__":
    enhance_datasets()
