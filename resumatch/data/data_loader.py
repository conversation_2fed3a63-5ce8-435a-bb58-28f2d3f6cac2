import os
import json
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Any, Optional
import random


class DataLoader:
    """
    Class for loading and processing resume and job description data.
    """

    def __init__(self, data_dir=None):
        """
        Initialize the data loader

        Args:
            data_dir (str, optional): Directory containing data files
        """
        self.data_dir = data_dir or os.path.join(os.path.dirname(os.path.abspath(__file__)), "datasets")
        os.makedirs(self.data_dir, exist_ok=True)

    def load_resume_data(self, file_path):
        """
        Load resume data from a JSON file

        Args:
            file_path (str): Path to the resume data file

        Returns:
            list: List of resume records
        """
        if not os.path.exists(file_path):
            return []

        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                return data
        except Exception as e:
            print(f"Error loading resume data: {e}")
            return []

    def load_job_data(self, file_path):
        """
        Load job description data from a JSON file

        Args:
            file_path (str): Path to the job data file

        Returns:
            list: List of job description records
        """
        if not os.path.exists(file_path):
            return []

        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                return data
        except Exception as e:
            print(f"Error loading job data: {e}")
            return []

    def save_resume_data(self, resume_data, file_path):
        """
        Save resume data to a JSON file

        Args:
            resume_data (list): List of resume records
            file_path (str): Path to save the data
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(resume_data, f, indent=2)

    def save_job_data(self, job_data, file_path):
        """
        Save job description data to a JSON file

        Args:
            job_data (list): List of job description records
            file_path (str): Path to save the data
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(job_data, f, indent=2)

    def generate_training_pairs(self, resume_embeddings, job_embeddings, match_data=None):
        """
        Generate training pairs for the Siamese network

        Args:
            resume_embeddings (dict): Dictionary mapping resume IDs to embeddings
            job_embeddings (dict): Dictionary mapping job IDs to embeddings
            match_data (list, optional): List of (resume_id, job_id, score) tuples
                If None, random pairs will be generated

        Returns:
            tuple: (resume_vectors, job_vectors, labels)
        """
        resume_vectors = []
        job_vectors = []
        labels = []

        # Check if we have any embeddings
        if not resume_embeddings or not job_embeddings:
            print("Warning: No embeddings available. Using random dummy embeddings for testing.")
            # Create dummy embeddings for testing
            embedding_dim = 384  # Default embedding dimension
            dummy_resume_embedding = np.random.rand(embedding_dim)
            dummy_job_embedding = np.random.rand(embedding_dim)

            # Create 5 positive pairs
            for _ in range(5):
                resume_vectors.append(dummy_resume_embedding + np.random.normal(0, 0.1, embedding_dim))
                job_vectors.append(dummy_job_embedding + np.random.normal(0, 0.1, embedding_dim))
                labels.append(1)

            # Create 5 negative pairs
            for _ in range(5):
                resume_vectors.append(dummy_resume_embedding + np.random.normal(0, 0.1, embedding_dim))
                job_vectors.append(np.random.rand(embedding_dim))  # Completely different embedding
                labels.append(0)

            return np.array(resume_vectors), np.array(job_vectors), np.array(labels)

        resume_ids = list(resume_embeddings.keys())
        job_ids = list(job_embeddings.keys())

        # If we have match data, use it
        if match_data:
            for resume_id, job_id, score in match_data:
                if resume_id in resume_embeddings and job_id in job_embeddings:
                    resume_vectors.append(resume_embeddings[resume_id])
                    job_vectors.append(job_embeddings[job_id])
                    # Convert score to binary label (1 for match, 0 for non-match)
                    labels.append(1 if score >= 0.7 else 0)
        else:
            # Generate random pairs
            # Positive pairs (assume 20% of pairs are matches)
            num_positive = max(1, min(len(resume_ids), len(job_ids)) // 5)  # Ensure at least 1 positive pair
            for _ in range(num_positive):
                resume_idx = random.randint(0, len(resume_ids) - 1)
                job_idx = random.randint(0, len(job_ids) - 1)
                resume_vectors.append(resume_embeddings[resume_ids[resume_idx]])
                job_vectors.append(job_embeddings[job_ids[job_idx]])
                labels.append(1)

            # Negative pairs
            num_negative = max(1, num_positive * 4)  # Ensure at least 1 negative pair
            for _ in range(num_negative):
                resume_idx = random.randint(0, len(resume_ids) - 1)
                job_idx = random.randint(0, len(job_ids) - 1)
                resume_vectors.append(resume_embeddings[resume_ids[resume_idx]])
                job_vectors.append(job_embeddings[job_ids[job_idx]])
                labels.append(0)

        return np.array(resume_vectors), np.array(job_vectors), np.array(labels)

    def split_data(self, resume_vectors, job_vectors, labels, test_size=0.2):
        """
        Split data into training and test sets

        Args:
            resume_vectors (np.ndarray): Resume embedding vectors
            job_vectors (np.ndarray): Job embedding vectors
            labels (np.ndarray): Binary labels
            test_size (float): Proportion of data to use for testing

        Returns:
            tuple: (train_resume, train_job, train_labels, test_resume, test_job, test_labels)
        """
        # Get indices for train and test splits
        indices = np.arange(len(labels))
        np.random.shuffle(indices)

        test_count = int(len(indices) * test_size)
        test_indices = indices[:test_count]
        train_indices = indices[test_count:]

        # Split the data
        train_resume = resume_vectors[train_indices]
        train_job = job_vectors[train_indices]
        train_labels = labels[train_indices]

        test_resume = resume_vectors[test_indices]
        test_job = job_vectors[test_indices]
        test_labels = labels[test_indices]

        return train_resume, train_job, train_labels, test_resume, test_job, test_labels

    def store_embeddings(self, embeddings_dict, file_path):
        """
        Store embeddings to file

        Args:
            embeddings_dict (dict): Dictionary of embeddings
            file_path (str): Path to save embeddings
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'wb') as f:
            np.save(f, embeddings_dict)

    def load_embeddings(self, file_path):
        """
        Load embeddings from file

        Args:
            file_path (str): Path to load embeddings from

        Returns:
            dict: Dictionary of embeddings
        """
        if not os.path.exists(file_path):
            return {}

        try:
            with open(file_path, 'rb') as f:
                return np.load(f, allow_pickle=True).item()
        except Exception as e:
            print(f"Error loading embeddings: {e}")
            return {}

    def load_kaggle_resume_dataset(self, path=None):
        """
        Load the processed Kaggle resume dataset

        Args:
            path (str, optional): Path to the processed resume dataset file

        Returns:
            list: List of resume records
        """
        if path is None:
            path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                               'data', 'datasets', 'processed_resumes.json')

        return self.load_resume_data(path)

    def generate_job_data_from_categories(self, resume_data, num_jobs_per_category=5, output_path=None):
        """
        Generate synthetic job posting data from resume categories

        Args:
            resume_data (list): List of resume records
            num_jobs_per_category (int): Number of jobs to generate per category
            output_path (str, optional): Path to save generated job data

        Returns:
            list: Generated job records
        """
        # Extract unique categories from resume data
        categories = set()
        for resume in resume_data:
            if 'category' in resume and resume['category'] != 'Unknown':
                categories.add(resume['category'])

        # Generate job descriptions for each category
        job_data = []
        job_id_counter = 0

        job_templates = [
            {
                "title": "{} Specialist",
                "description": "We are seeking a skilled {} specialist to join our team. The ideal candidate will have experience in {} and related fields.",
                "requirements": "Experience with {}. Strong background in {}. Bachelor's degree or higher in {} or related field."
            },
            {
                "title": "Senior {} Professional",
                "description": "Looking for an experienced {} professional to lead initiatives in our growing company.",
                "requirements": "5+ years of experience in {}. Demonstrated skills in {}. Advanced degree preferred."
            },
            {
                "title": "{} Manager",
                "description": "Join our team as a {} Manager responsible for overseeing {} operations and team members.",
                "requirements": "Management experience in {}. Strong leadership skills. Excellent communication skills."
            },
            {
                "title": "Junior {} Associate",
                "description": "Entry-level position for {} enthusiasts looking to start their career in this exciting field.",
                "requirements": "Basic understanding of {}. Eagerness to learn. Bachelor's degree in a related field."
            },
            {
                "title": "{} Consultant",
                "description": "We need consultants specialized in {} to help our clients achieve their goals effectively.",
                "requirements": "Consulting experience in {}. Project management skills. Client-facing experience."
            }
        ]

        for category in categories:
            # Get skills commonly associated with this category from resume data
            category_skills = []
            for resume in resume_data:
                if resume.get('category') == category and 'entities' in resume and 'skills' in resume['entities']:
                    category_skills.extend(resume['entities']['skills'])

            # Count skill occurrences and get most common ones
            skill_counter = {}
            for skill in category_skills:
                skill_counter[skill] = skill_counter.get(skill, 0) + 1

            common_skills = sorted(skill_counter.items(), key=lambda x: x[1], reverse=True)
            common_skills = [skill for skill, _ in common_skills[:10]]

            # Generate jobs for this category
            for _ in range(num_jobs_per_category):
                template = random.choice(job_templates)

                # Format template with category
                title = template["title"].format(category)
                description = template["description"].format(category, category, category)

                # Add some skills to requirements
                skills_to_use = random.sample(common_skills, min(3, len(common_skills))) if common_skills else []
                skills_text = ", ".join(skills_to_use)
                requirements = template["requirements"].format(
                    skills_text or category,
                    skills_text or category,
                    category
                )

                job_id = f"job_{job_id_counter}"
                job_id_counter += 1

                job_data.append({
                    "job_id": job_id,
                    "title": title,
                    "company": f"Company {job_id_counter}",
                    "category": category,
                    "description": description,
                    "requirements": requirements,
                    "responsibilities": f"Responsibilities include various tasks related to {category} and collaborating with team members."
                })

        # Save job data if output path is specified
        if output_path:
            self.save_job_data(job_data, output_path)

        return job_data

    def create_matched_training_data(self, resume_data, job_data, output_path=None):
        """
        Create training dataset with matched resume-job pairs based on categories

        Args:
            resume_data (list): List of resume records
            job_data (list): List of job records
            output_path (str, optional): Path to save matched data

        Returns:
            list: List of (resume_id, job_id, score) tuples
        """
        matches = []

        # Group jobs by category
        jobs_by_category = {}
        for job in job_data:
            category = job.get('category', 'Unknown')
            if category not in jobs_by_category:
                jobs_by_category[category] = []
            jobs_by_category[category].append(job)

        # For each resume, find matching jobs by category
        for resume in resume_data:
            resume_id = resume.get('resume_id')
            resume_category = resume.get('category', 'Unknown')

            if not resume_id or resume_category == 'Unknown':
                continue

            # Find jobs in the same category (positive matches)
            matching_jobs = jobs_by_category.get(resume_category, [])
            for job in matching_jobs[:3]:  # Limit to 3 matches per resume
                matches.append((resume_id, job['job_id'], 1.0))  # 1.0 score for same category

            # Add some negative examples from different categories
            other_categories = [c for c in jobs_by_category.keys() if c != resume_category]
            if other_categories:
                for _ in range(5):  # Add 5 negative examples
                    random_category = random.choice(other_categories)
                    random_jobs = jobs_by_category[random_category]
                    if random_jobs:
                        random_job = random.choice(random_jobs)
                        matches.append((resume_id, random_job['job_id'], 0.0))  # 0.0 score for different category

        # Save matches if output path is specified
        if output_path:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(matches, f, indent=2)

        return matches

    def prepare_kaggle_dataset_for_training(self, resume_path=None, output_dir=None):
        """
        Prepare the Kaggle resume dataset for training by generating job data and matches

        Args:
            resume_path (str, optional): Path to processed resume data
            output_dir (str, optional): Directory to save output files

        Returns:
            tuple: (resume_data, job_data, matches)
        """
        # Set default paths
        if resume_path is None:
            resume_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                     'data', 'datasets', 'processed_resumes.json')

        if output_dir is None:
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                    'data', 'datasets')

        os.makedirs(output_dir, exist_ok=True)

        # Load resume data
        print("Loading resume data...")
        resume_data = self.load_resume_data(resume_path)
        if not resume_data:
            print("Failed to load resume data!")
            return None, None, None

        # Generate job data
        print(f"Generating job data from {len(resume_data)} resumes...")
        job_path = os.path.join(output_dir, 'generated_jobs.json')
        job_data = self.generate_job_data_from_categories(resume_data, num_jobs_per_category=5, output_path=job_path)

        # Create matched training data
        print("Creating matched training data...")
        matches_path = os.path.join(output_dir, 'training_matches.json')
        matches = self.create_matched_training_data(resume_data, job_data, output_path=matches_path)

        print(f"Dataset preparation complete: {len(resume_data)} resumes, {len(job_data)} jobs, {len(matches)} matches")
        return resume_data, job_data, matches