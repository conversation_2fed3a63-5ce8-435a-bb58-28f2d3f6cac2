import uvicorn
import os
import sys
from pathlib import Path

# Add the parent directory to sys.path to allow importing from module
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from resumatch.api.router import app

if __name__ == "__main__":
    # Get port from environment variable or use default
    port = int(os.environ.get("PORT", 8000))
    
    # Run the FastAPI app with uvicorn
    uvicorn.run("resumatch.api.app:app", host="0.0.0.0", port=port, reload=True)