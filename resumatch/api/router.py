from fastapi import Fast<PERSON><PERSON>, File, UploadFile, Form, HTTPException, Depends, Query, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
import os
import uuid
import time
import logging
import base64
import json
import io
from datetime import datetime
import numpy as np
import torch
from functools import lru_cache

from resumatch.ocr.document_processor import DocumentProcessor
from resumatch.ner.entity_extractor import EntityExtractor
from resumatch.embeddings.text_embedder import TextEmbedder
from resumatch.siamese_network.matcher import ResumeMatcher
from resumatch.siamese_network.legacy_model import LegacyResumeMatcher

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("resumatch-api")

# Define API data models
class JobDescription(BaseModel):
    job_id: Optional[str] = None
    title: str
    company: str
    description: str
    requirements: Optional[str] = None
    responsibilities: Optional[str] = None
    location: Optional[str] = None
    date_posted: Optional[str] = None


class JobMatch(BaseModel):
    job_id: str
    title: str
    company: str
    match_score: float
    match_details: Optional[Dict[str, Any]] = None


class ResumeMatchResults(BaseModel):
    resume_id: str
    matches: List[JobMatch]


# Create FastAPI instance
app = FastAPI(
    title="ResuMatch API",
    description="API for matching resumes with job descriptions using AI",
    version="1.0.0"
)

# Enable CORS with more permissive configuration to avoid blocking issues
origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:3001",
    "http://127.0.0.1",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "*",  # Allow all origins for development purposes
    # Add any other origins that need access
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins to prevent CORS issues
    allow_origin_regex=r".*",  # Allow any origin pattern
    allow_credentials=False,  # Set to False when using wildcard origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["*"],  # Allow all headers
    expose_headers=[
        "Content-Type",
        "X-Content-Type-Options",
        "Content-Disposition",
        "Access-Control-Allow-Origin",
        "Access-Control-Allow-Methods",
        "Access-Control-Allow-Headers",
        "Access-Control-Max-Age"
    ],
    max_age=86400,  # Cache preflight requests for 24 hours to reduce OPTIONS requests
)

# Initialize components with improved models
logger.info("Initializing API components...")

# Document processor for OCR
document_processor = DocumentProcessor()
logger.info("Document processor initialized")

# Entity extractor with enhanced NER capabilities
entity_extractor = EntityExtractor()
logger.info("Entity extractor initialized")

# Text embedder with improved model
text_embedder = TextEmbedder()
logger.info(f"Text embedder initialized with model: {text_embedder.model_name}")
logger.info(f"Embedding dimension: {text_embedder.embedding_dim}")

# Load pre-trained matcher model
# Try to load the Kaggle-trained model first, then fall back to other models
model_paths = [
    os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "models", "siamese_model_kaggle.pth"),
    os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "models", "siamese_model_kaggle.pth"),
    os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "models", "siamese_model.pth"),
    os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "models", "siamese_model.pth")
]

resume_matcher = None
for model_path in model_paths:
    if os.path.exists(model_path):
        try:
            embedding_dim = text_embedder.embedding_dim
            # First try with the legacy model which is compatible with older saved weights
            try:
                resume_matcher = LegacyResumeMatcher(embedding_dim, model_path)
                logger.info(f"Loaded pre-trained model from: {model_path} using legacy model architecture")
                break
            except Exception as e:
                logger.warning(f"Could not load model with legacy architecture from {model_path}: {e}")
                # Try with the current model architecture
                resume_matcher = ResumeMatcher(embedding_dim, model_path)
                logger.info(f"Loaded pre-trained model from: {model_path} using current model architecture")
                break
        except Exception as e:
            logger.warning(f"Could not load model from {model_path}: {e}")

# If no model was loaded, create a new one
if resume_matcher is None:
    logger.warning("No pre-trained model found. Creating a new model.")
    embedding_dim = text_embedder.embedding_dim
    resume_matcher = ResumeMatcher(embedding_dim)

# Cache for storing computed similarities to improve performance
similarity_cache = {}

# In-memory storage for demo purposes
# In a production environment, use a database
jobs_db = {}
resumes_db = {}
job_embeddings = {}
resume_embeddings = {}


@app.get("/")
def root():
    return {"message": "Welcome to ResuMatch API", "version": "1.0.0"}


# Canonical and neutral endpoints only

@app.post("/api/documents/upload", response_model=Dict[str, str])
async def upload_document(
    file: UploadFile = File(...),
    name: str = Form(None),
    user_id: str = Form(None),
    background_tasks: BackgroundTasks = None
):
    return await process_document_upload(file, name, user_id, background_tasks)

@app.post("/api/documents/job", response_model=Dict[str, str])
async def add_document_job(job: JobDescription):
    return await process_job_description(job)

@app.get("/api/jobs", response_model=List[Dict[str, Any]])
async def list_jobs():
    return list(jobs_db.values())

@app.get("/api/jobs/{job_id}", response_model=Dict[str, Any])
async def get_job(job_id: str):
    if job_id not in jobs_db:
        raise HTTPException(status_code=404, detail="Job not found")
    return jobs_db[job_id]

@app.post("/api/documents/match", response_model=ResumeMatchResults)
@app.get("/api/documents/match", response_model=ResumeMatchResults)
async def match_document_to_jobs(
    resume_id: str = Query(None, description="Document ID to match"),
    threshold: float = Query(0.7, description="Minimum match score threshold (0-1)"),
    limit: int = Query(10, description="Maximum number of matches to return"),
    use_cache: bool = Query(True, description="Whether to use cached results if available"),
    job_id: str = Query(None, description="Optional job ID to filter results"),
    request: Request = None
):
    if resume_id is None and request:
        try:
            body = await request.json()
            resume_id = body.get('resume_id')
            if job_id is None and 'job_id' in body:
                job_id = body.get('job_id')
            logger.info(f"Got resume_id from request body: {resume_id}, job_id: {job_id}")
        except Exception as e:
            logger.warning(f"Failed to parse request body: {e}")
    if not resume_id:
        logger.error("No resume_id provided in query params or request body")
        raise HTTPException(status_code=400, detail="resume_id is required")
    return await process_resume_matching(resume_id, threshold, limit, use_cache, job_id)

@app.get("/api/resumes/{resume_id}", response_model=Dict[str, Any])
async def get_resume(resume_id: str):
    if resume_id not in resumes_db:
        raise HTTPException(status_code=404, detail="Resume not found")
    return resumes_db[resume_id]

async def process_document_upload(file: UploadFile, name: str = None, user_id: str = None, background_tasks: BackgroundTasks = None):
    """
    Handles the upload of a resume PDF, extracts text, stores metadata, and returns a unique resume_id.
    """
    if not file.filename.lower().endswith(".pdf"):
        raise HTTPException(status_code=400, detail="Only PDF files are supported.")

    # Generate a unique resume_id
    resume_id = str(uuid.uuid4())
    save_name = name or file.filename
    upload_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "data", "uploads")
    os.makedirs(upload_dir, exist_ok=True)
    file_path = os.path.join(upload_dir, f"{resume_id}.pdf")

    # Save the uploaded file
    with open(file_path, "wb") as f:
        content = await file.read()
        f.write(content)

    # Extract text from the PDF using DocumentProcessor
    try:
        extracted_text = document_processor.extract_text_from_pdf(file_path)
    except Exception as e:
        logger.error(f"Failed to extract text from PDF: {e}")
        raise HTTPException(status_code=500, detail="Failed to process PDF.")

    # Store resume metadata and extracted text in the in-memory DB
    resume_data = {
        "resume_id": resume_id,
        "name": save_name,
        "user_id": user_id,
        "file_path": file_path,
        "extracted_text": extracted_text,
        "uploaded_at": datetime.utcnow().isoformat()
    }
    resumes_db[resume_id] = resume_data

    # Compute and store embedding for the resume
    try:
        embedding = text_embedder.get_embeddings(extracted_text)[0]
        resume_embeddings[resume_id] = embedding
    except Exception as e:
        logger.warning(f"Failed to compute embedding for resume {resume_id}: {e}")

    logger.info(f"Resume uploaded and processed: {resume_id}")
    return {"resume_id": resume_id}

async def process_resume_matching(resume_id: str, threshold: float = 0.7, limit: int = 10, use_cache: bool = True, job_id: str = None):
    """
    Matches a resume to jobs using embeddings and the matcher model.
    """
    if resume_id not in resumes_db:
        raise HTTPException(status_code=404, detail="Resume not found")
    if resume_id not in resume_embeddings:
        # Try to recompute embedding from extracted_text if available
        resume_data = resumes_db.get(resume_id)
        if resume_data and "extracted_text" in resume_data:
            extracted_text = resume_data["extracted_text"]
            if not extracted_text or not extracted_text.strip():
                logger.error(f"Resume {resume_id} extracted_text is empty. Cannot compute embedding.")
                raise HTTPException(status_code=500, detail="Resume text is empty. Cannot compute embedding.")
            try:
                logger.info(f"Recomputing embedding for resume {resume_id}. Extracted text length: {len(extracted_text)}")
                embedding = text_embedder.get_embeddings(extracted_text)[0]
                resume_embeddings[resume_id] = embedding
                resume_embedding = embedding
                logger.info(f"Recomputed embedding for resume {resume_id}")
            except Exception as e:
                logger.error(f"Failed to recompute embedding for resume {resume_id}: {e}\nExtracted text: {extracted_text[:200]}...")
                raise HTTPException(status_code=500, detail=f"Failed to compute resume embedding: {e}")
        else:
            logger.error(f"Resume embedding not found and cannot be recomputed for resume {resume_id}.")
            raise HTTPException(status_code=500, detail="Resume embedding not found and cannot be recomputed.")
    else:
        resume_embedding = resume_embeddings[resume_id]
    matches = []

    # If a specific job_id is provided, only match against that job
    job_ids = [job_id] if job_id else list(jobs_db.keys())

    for jid in job_ids:
        job = jobs_db.get(jid)
        if not job:
            continue
        job_embedding = job_embeddings.get(jid)
        if job_embedding is None:
            # Compute embedding if not present
            job_text = job.get("description", "")
            try:
                job_embedding = text_embedder.get_embeddings(job_text)[0]
                job_embeddings[jid] = job_embedding
            except Exception as e:
                logger.warning(f"Failed to compute embedding for job {jid}: {e}")
                continue
        # Compute similarity (cosine similarity)
        try:
            score = float(resume_matcher.get_similarity(resume_embedding, job_embedding))
        except Exception as e:
            logger.warning(f"Failed to compute similarity for resume {resume_id} and job {jid}: {e}")
            continue
        if score >= threshold:
            matches.append(JobMatch(
                job_id=jid,
                title=job.get("title", ""),
                company=job.get("company", ""),
                match_score=score,
                match_details=None
            ))
    # Sort matches by score descending
    matches.sort(key=lambda m: m.match_score, reverse=True)
    matches = matches[:limit]
    return ResumeMatchResults(resume_id=resume_id, matches=matches)