import os
import re

class DocumentProcessor:
    """
    Simple class to handle document processing for resumes and job descriptions.
    Supports text files only in this simplified version.
    """

    def __init__(self, tesseract_cmd=None):
        """
        Initialize document processor

        Args:
            tesseract_cmd: Ignored, kept for compatibility
        """
        print("Using simplified document processor (text files only)")

    def extract_text_from_pdf(self, file_path_or_bytes):
        """
        Extract text from PDF files using a basic approach

        Args:
            file_path_or_bytes: File path (str) or bytes of the PDF file

        Returns:
            str: Extracted text from PDF
        """
        try:
            # Try to use PyPDF2 if available
            try:
                import PyPDF2

                if isinstance(file_path_or_bytes, bytes):
                    # Write bytes to temporary file
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                        temp_path = temp_file.name
                        temp_file.write(file_path_or_bytes)

                    # Extract text from the temporary file
                    with open(temp_path, 'rb') as file:
                        reader = PyPDF2.PdfReader(file)
                        text = ""
                        for page_num in range(len(reader.pages)):
                            text += reader.pages[page_num].extract_text() + "\n"

                    # Clean up temporary file
                    os.remove(temp_path)
                else:
                    # Extract text from the file path
                    with open(file_path_or_bytes, 'rb') as file:
                        reader = PyPDF2.PdfReader(file)
                        text = ""
                        for page_num in range(len(reader.pages)):
                            text += reader.pages[page_num].extract_text() + "\n"

                if text.strip():
                    return self._clean_text(text)
            except ImportError:
                print("PyPDF2 not available, trying alternative method")

            # If PyPDF2 is not available or extraction failed, try subprocess with pdftotext
            try:
                import subprocess
                import tempfile

                if isinstance(file_path_or_bytes, bytes):
                    # Write bytes to temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                        temp_path = temp_file.name
                        temp_file.write(file_path_or_bytes)
                    pdf_path = temp_path
                else:
                    pdf_path = file_path_or_bytes

                # Create a temporary file for the output text
                with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_out:
                    out_path = temp_out.name

                # Try to use pdftotext if available
                try:
                    subprocess.run(['pdftotext', pdf_path, out_path], check=True)
                    with open(out_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()

                    # Clean up temporary files
                    if isinstance(file_path_or_bytes, bytes):
                        os.remove(pdf_path)
                    os.remove(out_path)

                    if text.strip():
                        return self._clean_text(text)
                except (subprocess.SubprocessError, FileNotFoundError):
                    print("pdftotext not available or failed")
                    # Clean up temporary files
                    if isinstance(file_path_or_bytes, bytes):
                        os.remove(pdf_path)
                    os.remove(out_path)
            except Exception as e:
                print(f"Subprocess method failed: {e}")

            # If all methods fail, use a minimal placeholder that won't introduce incorrect information
            print("All PDF extraction methods failed, using minimal placeholder")
            return """
            Resume Content (PDF extraction limited)

            Please install PyPDF2 or pdftotext for better PDF extraction.
            """

        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return "Error extracting text from PDF. Please try a different file format."

    def extract_text_from_image(self, file_path_or_bytes):
        """
        Extract text from image files using a basic approach

        Args:
            file_path_or_bytes: File path (str) or bytes of the image

        Returns:
            str: Extracted text from image
        """
        try:
            # Try to use pytesseract if available
            try:
                import pytesseract
                from PIL import Image
                import io

                if isinstance(file_path_or_bytes, bytes):
                    image = Image.open(io.BytesIO(file_path_or_bytes))
                else:
                    image = Image.open(file_path_or_bytes)

                text = pytesseract.image_to_string(image)
                if text.strip():
                    return self._clean_text(text)
            except ImportError:
                print("pytesseract not available")

            # If all methods fail, use a minimal placeholder that won't introduce incorrect information
            print("Image OCR failed, using minimal placeholder")
            return """
            Resume Content (Image OCR limited)

            Please install pytesseract for better image text extraction.
            """

        except Exception as e:
            print(f"Error extracting text from image: {e}")
            return "Error extracting text from image. Please try a different file format."

    def extract_text_from_docx(self, file_path_or_bytes):
        """
        Extract text from DOCX files

        Args:
            file_path_or_bytes: File path (str) or bytes of the DOCX file

        Returns:
            str: Extracted text from DOCX
        """
        try:
            # Try to use python-docx if available
            try:
                import docx
                import tempfile

                if isinstance(file_path_or_bytes, bytes):
                    # Write bytes to temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                        temp_path = temp_file.name
                        temp_file.write(file_path_or_bytes)

                    # Extract text from the temporary file
                    doc = docx.Document(temp_path)
                    text = "\n".join([paragraph.text for paragraph in doc.paragraphs])

                    # Clean up temporary file
                    os.remove(temp_path)
                else:
                    # Extract text from the file path
                    doc = docx.Document(file_path_or_bytes)
                    text = "\n".join([paragraph.text for paragraph in doc.paragraphs])

                if text.strip():
                    return self._clean_text(text)
            except ImportError:
                print("python-docx not available")

            # If all methods fail, use a minimal placeholder that won't introduce incorrect information
            print("DOCX extraction failed, using minimal placeholder")
            return """
            Resume Content (DOCX extraction limited)

            Please install python-docx for better DOCX extraction.
            """

        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return "Error extracting text from DOCX. Please try a different file format."

    def extract_text_from_file(self, file_path_or_bytes, file_type=None):
        """
        Extract text from a file based on its type

        Args:
            file_path_or_bytes: File path (str) or bytes of the file
            file_type (str, optional): Type of file ('pdf', 'image', 'text', 'docx')
                                     If not provided, type is inferred from file extension

        Returns:
            str: Extracted text from file
        """
        if file_type is None:
            # Try to infer file type from extension
            if isinstance(file_path_or_bytes, str):
                file_extension = os.path.splitext(file_path_or_bytes)[1].lower()
                if file_extension in ['.pdf']:
                    file_type = 'pdf'
                elif file_extension in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
                    file_type = 'image'
                elif file_extension in ['.docx']:
                    file_type = 'docx'
                elif file_extension in ['.txt', '.doc']:
                    file_type = 'text'
            else:
                # Default to PDF for bytes if type not specified
                file_type = 'pdf'

        if file_type == 'pdf':
            return self.extract_text_from_pdf(file_path_or_bytes)
        elif file_type == 'image':
            return self.extract_text_from_image(file_path_or_bytes)
        elif file_type == 'docx':
            return self.extract_text_from_docx(file_path_or_bytes)
        elif file_type == 'text':
            if isinstance(file_path_or_bytes, bytes):
                return self._clean_text(file_path_or_bytes.decode('utf-8', errors='ignore'))
            else:
                try:
                    with open(file_path_or_bytes, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()
                    return self._clean_text(text)
                except Exception as e:
                    print(f"Error reading text file: {e}")
                    return "Error reading text file. Please try a different file format."
        else:
            raise ValueError(f"Unsupported file type: {file_type}")

    def extract_text(self, file_path):
        """
        Extract text from a file (wrapper for extract_text_from_file)

        Args:
            file_path (str): Path to the file

        Returns:
            str: Extracted text
        """
        return self.extract_text_from_file(file_path)

    def _clean_text(self, text):
        """
        Clean extracted text by removing extra whitespace and special characters

        Args:
            text (str): Text to clean

        Returns:
            str: Cleaned text
        """
        # Replace multiple whitespace with single space
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters that don't add meaning
        text = re.sub(r'[^\w\s.,;:!?()-]', '', text)
        return text.strip()