import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import re
import os

class SiameseNetwork(nn.Module):
    """
    Siamese neural network for matching resume embeddings with job description embeddings.
    Maps both types of documents into a shared semantic space for comparison.
    """

    def __init__(self, input_dim, hidden_dim=128, output_dim=64):
        """
        Initialize the Siamese Network

        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
        """
        super(SiameseNetwork, self).__init__()

        # Define network architecture without batch normalization
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, output_dim),
        )

    def forward(self, x):
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        return self.encoder(x)

    def forward_pair(self, x1, x2):
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2


class ResumeMatcher:
    """
    Class to match resumes with job descriptions using a Siamese network.
    """

    def __init__(self, embedding_dim, model_path=None):
        """
        Initialize the ResumeMatcher

        Args:
            embedding_dim (int): Dimension of input embeddings
            model_path (str, optional): Path to pre-trained model weights
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = SiameseNetwork(embedding_dim, hidden_dim=256, output_dim=128).to(self.device)

        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()

        # Loss function: Contrastive Loss
        self.criterion = ContrastiveLoss(margin=1.0)
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001)

    def train(self, resume_embeddings, job_embeddings, labels, num_epochs=10, batch_size=32):
        """
        Train the Siamese network with pairs of resume and job embeddings

        Args:
            resume_embeddings (np.ndarray): Array of resume embeddings
            job_embeddings (np.ndarray): Array of job description embeddings
            labels (np.ndarray): Binary labels (1 for match, 0 for non-match)
            num_epochs (int): Number of training epochs
            batch_size (int): Batch size for training

        Returns:
            list: Training losses
        """
        # Convert numpy arrays to torch tensors
        resume_embeddings = torch.tensor(resume_embeddings, dtype=torch.float32).to(self.device)
        job_embeddings = torch.tensor(job_embeddings, dtype=torch.float32).to(self.device)
        labels = torch.tensor(labels, dtype=torch.float32).to(self.device)

        dataset_size = len(labels)
        indices = list(range(dataset_size))

        # Training loop
        self.model.train()
        losses = []

        for epoch in range(num_epochs):
            # Shuffle indices for each epoch
            np.random.shuffle(indices)
            epoch_loss = 0

            # Process in batches
            for i in range(0, dataset_size, batch_size):
                # Get batch indices
                batch_indices = indices[i:min(i + batch_size, dataset_size)]

                # Get batch data
                resume_batch = resume_embeddings[batch_indices]
                job_batch = job_embeddings[batch_indices]
                label_batch = labels[batch_indices]

                # Clear gradients
                self.optimizer.zero_grad()

                # Forward pass
                output1, output2 = self.model.forward_pair(resume_batch, job_batch)

                # Calculate loss
                loss = self.criterion(output1, output2, label_batch)

                # Backward pass and optimization
                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()

            num_batches = max(1, dataset_size // batch_size)  # Ensure at least 1 batch
            avg_epoch_loss = epoch_loss / num_batches
            losses.append(avg_epoch_loss)
            print(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_epoch_loss:.4f}")

        return losses

    def match(self, resume_embedding, job_embeddings_list, similarity_threshold=0.7):
        """
        Match a resume with multiple job descriptions

        Args:
            resume_embedding (np.ndarray): Embedding of a resume
            job_embeddings_list (List[np.ndarray]): List of job description embeddings
            similarity_threshold (float): Threshold for considering a match

        Returns:
            List[Tuple[int, float]]: List of (job_index, similarity_score) sorted by similarity
        """
        self.model.eval()

        # Convert resume embedding to tensor
        resume_tensor = torch.tensor(resume_embedding, dtype=torch.float32).to(self.device)

        matches = []

        with torch.no_grad():
            # Encode resume
            resume_encoded = self.model(resume_tensor)

            # Process each job embedding
            for i, job_embedding in enumerate(job_embeddings_list):
                # Convert job embedding to tensor
                job_tensor = torch.tensor(job_embedding, dtype=torch.float32).to(self.device)

                # Encode job
                job_encoded = self.model(job_tensor)

                # Calculate cosine similarity
                similarity = self._cosine_similarity(resume_encoded, job_encoded).item()

                if similarity >= similarity_threshold:
                    matches.append((i, similarity))

        # Sort matches by similarity score (descending)
        matches.sort(key=lambda x: x[1], reverse=True)

        return matches

    def _cosine_similarity(self, x1, x2):
        """Calculate cosine similarity between two vectors"""
        return torch.nn.functional.cosine_similarity(x1, x2, dim=1)

    def get_similarity(self, resume_embedding, job_embedding, resume_text=None, job_text=None):
        """
        Calculate enhanced similarity between a resume and job embedding with skill-aware scoring

        Args:
            resume_embedding (np.ndarray): Resume embedding vector
            job_embedding (np.ndarray): Job embedding vector
            resume_text (str, optional): Resume text for skill analysis
            job_text (str, optional): Job text for skill analysis

        Returns:
            float: Similarity score between 0 and 1
        """
        self.model.eval()

        # Convert embeddings to tensors
        resume_tensor = torch.tensor(resume_embedding, dtype=torch.float32).to(self.device)
        job_tensor = torch.tensor(job_embedding, dtype=torch.float32).to(self.device)

        with torch.no_grad():
            try:
                # Calculate base similarity using embeddings
                base_similarity = self._calculate_base_similarity(resume_tensor, job_tensor, resume_embedding, job_embedding)

                # If we have text data, enhance with skill-aware scoring
                if resume_text and job_text:
                    enhanced_similarity = self._calculate_enhanced_similarity(base_similarity, resume_text, job_text)
                    return enhanced_similarity
                else:
                    # Apply improved scaling for embedding-only similarity
                    return self._apply_improved_scaling(base_similarity)

            except Exception as e:
                # Fallback to direct cosine similarity if anything fails
                return self._calculate_direct_cosine_similarity(resume_embedding, job_embedding)

    def _calculate_base_similarity(self, resume_tensor, job_tensor, resume_embedding, job_embedding):
        """Calculate base similarity using both Siamese network and direct cosine similarity"""
        try:
            # Encode both embeddings using Siamese network
            resume_encoded = self.model(resume_tensor)
            job_encoded = self.model(job_tensor)

            # Calculate cosine similarity from Siamese network
            siamese_similarity = self._cosine_similarity(resume_encoded, job_encoded).item()
        except:
            siamese_similarity = 0.5  # Neutral fallback

        # Calculate direct cosine similarity between original embeddings
        direct_similarity = self._calculate_direct_cosine_similarity(resume_embedding, job_embedding)

        # Combine similarities with adjusted weights
        # Reduce Siamese network weight since it's not well-trained for our specific use case
        combined_similarity = 0.2 * siamese_similarity + 0.8 * direct_similarity

        return combined_similarity

    def _calculate_direct_cosine_similarity(self, embedding1, embedding2):
        """Calculate direct cosine similarity between two embeddings"""
        # Ensure embeddings are numpy arrays
        if isinstance(embedding1, torch.Tensor):
            embedding1 = embedding1.cpu().numpy()
        if isinstance(embedding2, torch.Tensor):
            embedding2 = embedding2.cpu().numpy()

        # Flatten if needed
        embedding1 = embedding1.flatten()
        embedding2 = embedding2.flatten()

        # Calculate cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)

        # Ensure similarity is between 0 and 1
        similarity = max(0.0, min(1.0, (similarity + 1) / 2))  # Convert from [-1,1] to [0,1]

        return similarity

    def _calculate_enhanced_similarity(self, base_similarity, resume_text, job_text):
        """Calculate enhanced similarity using skill matching and domain analysis"""

        # Extract key skills and technologies from both texts
        resume_skills = self._extract_key_skills(resume_text.lower())
        job_skills = self._extract_key_skills(job_text.lower())

        # Calculate skill overlap score
        skill_overlap_score = self._calculate_skill_overlap(resume_skills, job_skills)

        # Calculate domain relevance score
        domain_score = self._calculate_domain_relevance(resume_text.lower(), job_text.lower())

        # Calculate experience level alignment
        experience_score = self._calculate_experience_alignment(resume_text.lower(), job_text.lower())

        # Combine all scores with weights
        # Base similarity: 40%, Skill overlap: 35%, Domain relevance: 15%, Experience: 10%
        enhanced_similarity = (
            0.40 * base_similarity +
            0.35 * skill_overlap_score +
            0.15 * domain_score +
            0.10 * experience_score
        )

        # Apply final scaling to ensure proper score distribution
        return self._apply_enhanced_scaling(enhanced_similarity, skill_overlap_score, domain_score)

    def _extract_key_skills(self, text):
        """Extract key technical skills from text"""
        # Define comprehensive skill categories with weights
        skill_patterns = {
            # High-value mobile/app development skills
            'mobile_dev': ['flutter', 'dart', 'android', 'ios', 'react native', 'xamarin', 'ionic', 'cordova'],
            'mobile_frameworks': ['riverpod', 'bloc', 'provider', 'getx', 'redux'],
            'mobile_tools': ['android studio', 'xcode', 'firebase', 'play store', 'app store'],

            # Programming languages
            'languages': ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust', 'swift', 'kotlin'],

            # Web technologies
            'web_frontend': ['react', 'angular', 'vue', 'html', 'css', 'bootstrap', 'tailwind'],
            'web_backend': ['fastapi', 'django', 'flask', 'express', 'spring', 'asp.net'],

            # Databases
            'databases': ['mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'firestore'],

            # Cloud and DevOps
            'cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins'],

            # APIs and protocols
            'apis': ['rest', 'graphql', 'websocket', 'json', 'xml', 'grpc'],

            # Data science
            'data_science': ['tensorflow', 'pytorch', 'pandas', 'numpy', 'scikit-learn', 'matplotlib'],

            # Other technical
            'other_tech': ['git', 'github', 'linux', 'sql', 'nosql', 'microservices']
        }

        found_skills = {}
        for category, skills in skill_patterns.items():
            found_skills[category] = []
            for skill in skills:
                if re.search(r'\b' + re.escape(skill) + r'\b', text):
                    found_skills[category].append(skill)

        return found_skills

    def _calculate_skill_overlap(self, resume_skills, job_skills):
        """Calculate skill overlap score with category weighting"""
        # Define category weights based on importance for matching
        category_weights = {
            'mobile_dev': 3.0,      # Highest weight for mobile development
            'mobile_frameworks': 2.5,
            'mobile_tools': 2.0,
            'languages': 2.0,
            'web_frontend': 1.5,
            'web_backend': 1.8,
            'databases': 1.3,
            'cloud': 1.2,
            'apis': 1.5,
            'data_science': 1.0,
            'other_tech': 1.0
        }

        total_weighted_overlap = 0
        total_weighted_job_skills = 0

        for category in resume_skills.keys():
            resume_cat_skills = set(resume_skills[category])
            job_cat_skills = set(job_skills.get(category, []))

            if job_cat_skills:  # Only consider categories that the job requires
                overlap = len(resume_cat_skills.intersection(job_cat_skills))
                total_job_skills = len(job_cat_skills)

                weight = category_weights.get(category, 1.0)

                total_weighted_overlap += overlap * weight
                total_weighted_job_skills += total_job_skills * weight

        if total_weighted_job_skills == 0:
            return 0.3  # Low score if no technical skills found in job

        skill_score = total_weighted_overlap / total_weighted_job_skills
        return min(1.0, skill_score)  # Cap at 1.0

    def _calculate_domain_relevance(self, resume_text, job_text):
        """Calculate domain relevance score"""
        # Define domain keywords with weights
        domain_keywords = {
            'mobile_app': ['mobile', 'app', 'application', 'smartphone', 'tablet'],
            'web_dev': ['web', 'website', 'frontend', 'backend', 'fullstack', 'full stack'],
            'data_science': ['data science', 'machine learning', 'ai', 'analytics', 'big data'],
            'devops': ['devops', 'infrastructure', 'deployment', 'ci/cd', 'automation'],
            'design': ['ui', 'ux', 'design', 'user interface', 'user experience'],
            'business': ['marketing', 'sales', 'business', 'management', 'strategy'],
            'finance': ['accounting', 'finance', 'financial', 'budget', 'audit'],
            'engineering': ['mechanical', 'electrical', 'civil', 'manufacturing']
        }

        resume_domains = set()
        job_domains = set()

        for domain, keywords in domain_keywords.items():
            for keyword in keywords:
                if re.search(r'\b' + re.escape(keyword) + r'\b', resume_text):
                    resume_domains.add(domain)
                if re.search(r'\b' + re.escape(keyword) + r'\b', job_text):
                    job_domains.add(domain)

        if not job_domains:
            return 0.5  # Neutral if no clear domain

        overlap = len(resume_domains.intersection(job_domains))
        total_job_domains = len(job_domains)

        return overlap / total_job_domains if total_job_domains > 0 else 0.5

    def _calculate_experience_alignment(self, resume_text, job_text):
        """Calculate experience level alignment"""
        # Extract experience indicators
        resume_exp_indicators = []
        job_exp_indicators = []

        # Experience patterns
        exp_patterns = {
            'entry': ['entry', 'junior', 'graduate', 'intern', '0-2 years', '1-2 years'],
            'mid': ['mid', 'intermediate', '2-5 years', '3-5 years', '2+ years'],
            'senior': ['senior', 'lead', 'principal', '5+ years', '7+ years', 'expert']
        }

        for level, patterns in exp_patterns.items():
            for pattern in patterns:
                if re.search(r'\b' + re.escape(pattern) + r'\b', resume_text):
                    resume_exp_indicators.append(level)
                if re.search(r'\b' + re.escape(pattern) + r'\b', job_text):
                    job_exp_indicators.append(level)

        # If no clear indicators, return neutral
        if not resume_exp_indicators or not job_exp_indicators:
            return 0.6

        # Check for alignment
        if any(level in job_exp_indicators for level in resume_exp_indicators):
            return 0.8
        else:
            return 0.4

    def _apply_enhanced_scaling(self, similarity, skill_overlap, domain_score):
        """Apply enhanced scaling based on skill overlap and domain relevance"""
        import math

        # If there's very low skill overlap or domain mismatch, cap the score
        if skill_overlap < 0.1 and domain_score < 0.3:
            return min(0.35, similarity * 0.8)  # Cap very low relevance jobs

        # If there's good skill overlap, allow higher scores
        if skill_overlap > 0.6 and domain_score > 0.7:
            # Boost high-relevance matches
            boosted = similarity * 1.1
            return min(0.95, boosted)

        # Apply sigmoid-like scaling for better distribution
        scaled = 1 / (1 + math.exp(-10 * (similarity - 0.5)))

        # Map to desired range: 0.15 to 0.90
        final_score = 0.15 + (scaled * 0.75)

        return min(0.90, max(0.15, final_score))

    def _apply_improved_scaling(self, similarity):
        """Apply improved scaling for embedding-only similarity"""
        import math

        # Apply more aggressive scaling to spread out scores
        # Use a power function to reduce high similarities and boost low ones
        if similarity > 0.7:
            # Reduce very high similarities
            scaled = 0.7 + (similarity - 0.7) * 0.5
        else:
            # Keep lower similarities more linear
            scaled = similarity

        # Apply sigmoid transformation for better distribution
        sigmoid_scaled = 1 / (1 + math.exp(-8 * (scaled - 0.5)))

        # Map to range 0.2 to 0.85
        final_score = 0.2 + (sigmoid_scaled * 0.65)

        return min(0.85, max(0.20, final_score))

    def save_model(self, path="models/siamese_model.pth"):
        """Save the model weights"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(self.model.state_dict(), path)
        print(f"Model saved to {path}")

    def evaluate(self, resume_embeddings, job_embeddings, true_labels):
        """
        Evaluate the model on a test set

        Args:
            resume_embeddings (np.ndarray): Array of resume embeddings
            job_embeddings (np.ndarray): Array of job description embeddings
            true_labels (np.ndarray): Binary labels (1 for match, 0 for non-match)

        Returns:
            dict: Evaluation metrics (accuracy, precision, recall, f1)
        """
        self.model.eval()

        # Convert numpy arrays to torch tensors
        resume_tensor = torch.tensor(resume_embeddings, dtype=torch.float32).to(self.device)
        job_tensor = torch.tensor(job_embeddings, dtype=torch.float32).to(self.device)

        with torch.no_grad():
            # Encode inputs
            resume_encoded = self.model(resume_tensor)
            job_encoded = self.model(job_tensor)

            # Calculate similarity scores
            similarity_scores = self._cosine_similarity(resume_encoded, job_encoded).cpu().numpy()

        # Convert similarity scores to predicted labels (1 if score >= 0.7, 0 otherwise)
        predicted_labels = (similarity_scores >= 0.7).astype(int)

        # Calculate evaluation metrics
        accuracy = np.mean(predicted_labels == true_labels)
        precision = np.sum((predicted_labels == 1) & (true_labels == 1)) / (np.sum(predicted_labels == 1) + 1e-10)
        recall = np.sum((predicted_labels == 1) & (true_labels == 1)) / (np.sum(true_labels == 1) + 1e-10)
        f1 = 2 * precision * recall / (precision + recall + 1e-10)

        return {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1': float(f1)
        }


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss function for Siamese networks.
    Pushes similar pairs together and dissimilar pairs apart in the embedding space.
    """

    def __init__(self, margin=1.0):
        """
        Initialize the contrastive loss

        Args:
            margin (float): Margin for dissimilar pairs
        """
        super(ContrastiveLoss, self).__init__()
        self.margin = margin

    def forward(self, output1, output2, label):
        """
        Calculate the contrastive loss

        Args:
            output1: Output of the first input
            output2: Output of the second input
            label: Binary label (1 for similar pair, 0 for dissimilar pair)

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate euclidean distance
        euclidean_distance = torch.nn.functional.pairwise_distance(output1, output2)

        # Contrastive loss calculation
        loss = torch.mean((1 - label) * torch.pow(euclidean_distance, 2) +  # Similar pairs: minimize distance
                         (label) * torch.pow(torch.clamp(self.margin - euclidean_distance, min=0.0), 2))  # Dissimilar pairs: push apart

        return loss


class TripletLoss(nn.Module):
    """
    Triplet loss function for Siamese networks.
    Pushes an anchor closer to a positive example and farther from a negative example.
    """

    def __init__(self, margin=1.0):
        """
        Initialize the triplet loss

        Args:
            margin (float): Margin between positive and negative distances
        """
        super(TripletLoss, self).__init__()
        self.margin = margin

    def forward(self, anchor, positive, negative):
        """
        Calculate the triplet loss

        Args:
            anchor: Anchor embedding
            positive: Positive embedding (same class as anchor)
            negative: Negative embedding (different class from anchor)

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate distances
        pos_dist = torch.nn.functional.pairwise_distance(anchor, positive)
        neg_dist = torch.nn.functional.pairwise_distance(anchor, negative)

        # Triplet loss calculation
        loss = torch.mean(torch.clamp(pos_dist - neg_dist + self.margin, min=0.0))

        return loss


class CosineSimilarityLoss(nn.Module):
    """
    Cosine similarity loss function for Siamese networks.
    Maximizes cosine similarity for similar pairs and minimizes it for dissimilar pairs.
    """

    def __init__(self, temperature=0.5):
        """
        Initialize the cosine similarity loss

        Args:
            temperature (float): Temperature parameter to scale the similarity
        """
        super(CosineSimilarityLoss, self).__init__()
        self.temperature = temperature
        self.cos_sim = nn.CosineSimilarity(dim=1, eps=1e-8)

    def forward(self, output1, output2, label):
        """
        Calculate the cosine similarity loss

        Args:
            output1: Output of the first input
            output2: Output of the second input
            label: Binary label (1 for similar pair, 0 for dissimilar pair)

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate cosine similarity
        similarity = self.cos_sim(output1, output2)

        # Scale similarity by temperature
        similarity = similarity / self.temperature

        # Convert labels to -1 (dissimilar) and 1 (similar)
        target = 2 * label - 1

        # Loss calculation: similar pairs should have high similarity, dissimilar pairs should have low similarity
        loss = torch.mean((1 - target * similarity) / 2)

        return loss


class OnlineContrastiveLoss(nn.Module):
    """
    Online contrastive loss for Siamese networks.
    Selects hard positive and negative pairs within a batch for more efficient training.
    """

    def __init__(self, margin=1.0, mining='hard'):
        """
        Initialize the online contrastive loss

        Args:
            margin (float): Margin for dissimilar pairs
            mining (str): Mining strategy ('hard', 'semi-hard', or 'all')
        """
        super(OnlineContrastiveLoss, self).__init__()
        self.margin = margin
        self.mining = mining

    def forward(self, embeddings, labels):
        """
        Calculate the online contrastive loss

        Args:
            embeddings: Batch of embeddings
            labels: Batch of labels

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate pairwise distances
        distances = torch.cdist(embeddings, embeddings, p=2)

        # Create mask for positive and negative pairs
        n = labels.size(0)
        mask_pos = labels.expand(n, n).eq(labels.expand(n, n).t())
        mask_neg = ~mask_pos

        # Remove diagonal elements (self-comparisons)
        mask_pos = mask_pos.clone()
        mask_pos.fill_diagonal_(False)

        # Select pairs based on mining strategy
        if self.mining == 'hard':
            # Hard mining: select hardest positive and negative pairs
            hardest_pos = distances.masked_select(mask_pos).max()
            hardest_neg = distances.masked_select(mask_neg).min()
            loss = torch.clamp(hardest_pos - hardest_neg + self.margin, min=0.0)

        elif self.mining == 'semi-hard':
            # Semi-hard mining: select semi-hard negative pairs
            pos_pairs = distances.masked_select(mask_pos).view(n, -1)
            neg_pairs = distances.masked_select(mask_neg).view(n, -1)

            # Find semi-hard negatives: negatives that are farther than positives but within margin
            semi_hard_mask = (neg_pairs > pos_pairs.min(dim=1, keepdim=True)[0]) & (neg_pairs < pos_pairs.min(dim=1, keepdim=True)[0] + self.margin)

            if semi_hard_mask.sum() > 0:
                semi_hard_neg = neg_pairs.masked_select(semi_hard_mask).mean()
                loss = torch.clamp(pos_pairs.mean() - semi_hard_neg + self.margin, min=0.0)
            else:
                # Fall back to all pairs if no semi-hard negatives found
                loss = torch.clamp(pos_pairs.mean() - neg_pairs.mean() + self.margin, min=0.0)

        else:  # 'all' mining
            # Use all pairs
            pos_loss = distances.masked_select(mask_pos).mean()
            neg_loss = torch.clamp(self.margin - distances.masked_select(mask_neg), min=0.0).mean()
            loss = pos_loss + neg_loss

        return loss