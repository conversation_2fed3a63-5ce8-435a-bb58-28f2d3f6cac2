import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple
import os
import json
from datetime import datetime

class SiameseNetwork(nn.Module):
    """
    Siamese neural network for matching resume embeddings with job description embeddings.
    Maps both types of documents into a shared semantic space for comparison.
    """

    def __init__(self, input_dim, hidden_dim=128, output_dim=64):
        """
        Initialize the Siamese Network

        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
        """
        super(SiameseNetwork, self).__init__()

        # Define network architecture without batch normalization
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, output_dim),
        )

    def forward(self, x):
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        return self.encoder(x)

    def forward_pair(self, x1, x2):
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2


class ResumeMatcher:
    """
    Class to match resumes with job descriptions using a Siamese network.
    """

    def __init__(self, embedding_dim, model_path=None):
        """
        Initialize the ResumeMatcher

        Args:
            embedding_dim (int): Dimension of input embeddings
            model_path (str, optional): Path to pre-trained model weights
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = SiameseNetwork(embedding_dim, hidden_dim=256, output_dim=128).to(self.device)

        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()

        # Loss function: Contrastive Loss
        self.criterion = ContrastiveLoss(margin=1.0)
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001)

    def train(self, resume_embeddings, job_embeddings, labels, num_epochs=10, batch_size=32):
        """
        Train the Siamese network with pairs of resume and job embeddings

        Args:
            resume_embeddings (np.ndarray): Array of resume embeddings
            job_embeddings (np.ndarray): Array of job description embeddings
            labels (np.ndarray): Binary labels (1 for match, 0 for non-match)
            num_epochs (int): Number of training epochs
            batch_size (int): Batch size for training

        Returns:
            list: Training losses
        """
        # Convert numpy arrays to torch tensors
        resume_embeddings = torch.tensor(resume_embeddings, dtype=torch.float32).to(self.device)
        job_embeddings = torch.tensor(job_embeddings, dtype=torch.float32).to(self.device)
        labels = torch.tensor(labels, dtype=torch.float32).to(self.device)

        dataset_size = len(labels)
        indices = list(range(dataset_size))

        # Training loop
        self.model.train()
        losses = []

        for epoch in range(num_epochs):
            # Shuffle indices for each epoch
            np.random.shuffle(indices)
            epoch_loss = 0

            # Process in batches
            for i in range(0, dataset_size, batch_size):
                # Get batch indices
                batch_indices = indices[i:min(i + batch_size, dataset_size)]

                # Get batch data
                resume_batch = resume_embeddings[batch_indices]
                job_batch = job_embeddings[batch_indices]
                label_batch = labels[batch_indices]

                # Clear gradients
                self.optimizer.zero_grad()

                # Forward pass
                output1, output2 = self.model.forward_pair(resume_batch, job_batch)

                # Calculate loss
                loss = self.criterion(output1, output2, label_batch)

                # Backward pass and optimization
                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()

            num_batches = max(1, dataset_size // batch_size)  # Ensure at least 1 batch
            avg_epoch_loss = epoch_loss / num_batches
            losses.append(avg_epoch_loss)
            print(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_epoch_loss:.4f}")

        return losses

    def match(self, resume_embedding, job_embeddings_list, similarity_threshold=0.7):
        """
        Match a resume with multiple job descriptions

        Args:
            resume_embedding (np.ndarray): Embedding of a resume
            job_embeddings_list (List[np.ndarray]): List of job description embeddings
            similarity_threshold (float): Threshold for considering a match

        Returns:
            List[Tuple[int, float]]: List of (job_index, similarity_score) sorted by similarity
        """
        self.model.eval()

        # Convert resume embedding to tensor
        resume_tensor = torch.tensor(resume_embedding, dtype=torch.float32).to(self.device)

        matches = []

        with torch.no_grad():
            # Encode resume
            resume_encoded = self.model(resume_tensor)

            # Process each job embedding
            for i, job_embedding in enumerate(job_embeddings_list):
                # Convert job embedding to tensor
                job_tensor = torch.tensor(job_embedding, dtype=torch.float32).to(self.device)

                # Encode job
                job_encoded = self.model(job_tensor)

                # Calculate cosine similarity
                similarity = self._cosine_similarity(resume_encoded, job_encoded).item()

                if similarity >= similarity_threshold:
                    matches.append((i, similarity))

        # Sort matches by similarity score (descending)
        matches.sort(key=lambda x: x[1], reverse=True)

        return matches

    def _cosine_similarity(self, x1, x2):
        """Calculate cosine similarity between two vectors"""
        return torch.nn.functional.cosine_similarity(x1, x2, dim=1)

    def save_model(self, path="models/siamese_model.pth"):
        """Save the model weights"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(self.model.state_dict(), path)
        print(f"Model saved to {path}")

    def evaluate(self, resume_embeddings, job_embeddings, true_labels):
        """
        Evaluate the model on a test set

        Args:
            resume_embeddings (np.ndarray): Array of resume embeddings
            job_embeddings (np.ndarray): Array of job description embeddings
            true_labels (np.ndarray): Binary labels (1 for match, 0 for non-match)

        Returns:
            dict: Evaluation metrics (accuracy, precision, recall, f1)
        """
        self.model.eval()

        # Convert numpy arrays to torch tensors
        resume_tensor = torch.tensor(resume_embeddings, dtype=torch.float32).to(self.device)
        job_tensor = torch.tensor(job_embeddings, dtype=torch.float32).to(self.device)

        with torch.no_grad():
            # Encode inputs
            resume_encoded = self.model(resume_tensor)
            job_encoded = self.model(job_tensor)

            # Calculate similarity scores
            similarity_scores = self._cosine_similarity(resume_encoded, job_encoded).cpu().numpy()

        # Convert similarity scores to predicted labels (1 if score >= 0.7, 0 otherwise)
        predicted_labels = (similarity_scores >= 0.7).astype(int)

        # Calculate evaluation metrics
        accuracy = np.mean(predicted_labels == true_labels)
        precision = np.sum((predicted_labels == 1) & (true_labels == 1)) / (np.sum(predicted_labels == 1) + 1e-10)
        recall = np.sum((predicted_labels == 1) & (true_labels == 1)) / (np.sum(true_labels == 1) + 1e-10)
        f1 = 2 * precision * recall / (precision + recall + 1e-10)

        return {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1': float(f1)
        }


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss function for Siamese networks.
    Pushes similar pairs together and dissimilar pairs apart in the embedding space.
    """

    def __init__(self, margin=1.0):
        """
        Initialize the contrastive loss

        Args:
            margin (float): Margin for dissimilar pairs
        """
        super(ContrastiveLoss, self).__init__()
        self.margin = margin

    def forward(self, output1, output2, label):
        """
        Calculate the contrastive loss

        Args:
            output1: Output of the first input
            output2: Output of the second input
            label: Binary label (1 for similar pair, 0 for dissimilar pair)

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate euclidean distance
        euclidean_distance = torch.nn.functional.pairwise_distance(output1, output2)

        # Contrastive loss calculation
        loss = torch.mean((1 - label) * torch.pow(euclidean_distance, 2) +  # Similar pairs: minimize distance
                         (label) * torch.pow(torch.clamp(self.margin - euclidean_distance, min=0.0), 2))  # Dissimilar pairs: push apart

        return loss


class TripletLoss(nn.Module):
    """
    Triplet loss function for Siamese networks.
    Pushes an anchor closer to a positive example and farther from a negative example.
    """

    def __init__(self, margin=1.0):
        """
        Initialize the triplet loss

        Args:
            margin (float): Margin between positive and negative distances
        """
        super(TripletLoss, self).__init__()
        self.margin = margin

    def forward(self, anchor, positive, negative):
        """
        Calculate the triplet loss

        Args:
            anchor: Anchor embedding
            positive: Positive embedding (same class as anchor)
            negative: Negative embedding (different class from anchor)

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate distances
        pos_dist = torch.nn.functional.pairwise_distance(anchor, positive)
        neg_dist = torch.nn.functional.pairwise_distance(anchor, negative)

        # Triplet loss calculation
        loss = torch.mean(torch.clamp(pos_dist - neg_dist + self.margin, min=0.0))

        return loss


class CosineSimilarityLoss(nn.Module):
    """
    Cosine similarity loss function for Siamese networks.
    Maximizes cosine similarity for similar pairs and minimizes it for dissimilar pairs.
    """

    def __init__(self, temperature=0.5):
        """
        Initialize the cosine similarity loss

        Args:
            temperature (float): Temperature parameter to scale the similarity
        """
        super(CosineSimilarityLoss, self).__init__()
        self.temperature = temperature
        self.cos_sim = nn.CosineSimilarity(dim=1, eps=1e-8)

    def forward(self, output1, output2, label):
        """
        Calculate the cosine similarity loss

        Args:
            output1: Output of the first input
            output2: Output of the second input
            label: Binary label (1 for similar pair, 0 for dissimilar pair)

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate cosine similarity
        similarity = self.cos_sim(output1, output2)

        # Scale similarity by temperature
        similarity = similarity / self.temperature

        # Convert labels to -1 (dissimilar) and 1 (similar)
        target = 2 * label - 1

        # Loss calculation: similar pairs should have high similarity, dissimilar pairs should have low similarity
        loss = torch.mean((1 - target * similarity) / 2)

        return loss


class OnlineContrastiveLoss(nn.Module):
    """
    Online contrastive loss for Siamese networks.
    Selects hard positive and negative pairs within a batch for more efficient training.
    """

    def __init__(self, margin=1.0, mining='hard'):
        """
        Initialize the online contrastive loss

        Args:
            margin (float): Margin for dissimilar pairs
            mining (str): Mining strategy ('hard', 'semi-hard', or 'all')
        """
        super(OnlineContrastiveLoss, self).__init__()
        self.margin = margin
        self.mining = mining

    def forward(self, embeddings, labels):
        """
        Calculate the online contrastive loss

        Args:
            embeddings: Batch of embeddings
            labels: Batch of labels

        Returns:
            torch.Tensor: Loss value
        """
        # Calculate pairwise distances
        distances = torch.cdist(embeddings, embeddings, p=2)

        # Create mask for positive and negative pairs
        n = labels.size(0)
        mask_pos = labels.expand(n, n).eq(labels.expand(n, n).t())
        mask_neg = ~mask_pos

        # Remove diagonal elements (self-comparisons)
        mask_pos = mask_pos.clone()
        mask_pos.fill_diagonal_(False)

        # Select pairs based on mining strategy
        if self.mining == 'hard':
            # Hard mining: select hardest positive and negative pairs
            hardest_pos = distances.masked_select(mask_pos).max()
            hardest_neg = distances.masked_select(mask_neg).min()
            loss = torch.clamp(hardest_pos - hardest_neg + self.margin, min=0.0)

        elif self.mining == 'semi-hard':
            # Semi-hard mining: select semi-hard negative pairs
            pos_pairs = distances.masked_select(mask_pos).view(n, -1)
            neg_pairs = distances.masked_select(mask_neg).view(n, -1)

            # Find semi-hard negatives: negatives that are farther than positives but within margin
            semi_hard_mask = (neg_pairs > pos_pairs.min(dim=1, keepdim=True)[0]) & (neg_pairs < pos_pairs.min(dim=1, keepdim=True)[0] + self.margin)

            if semi_hard_mask.sum() > 0:
                semi_hard_neg = neg_pairs.masked_select(semi_hard_mask).mean()
                loss = torch.clamp(pos_pairs.mean() - semi_hard_neg + self.margin, min=0.0)
            else:
                # Fall back to all pairs if no semi-hard negatives found
                loss = torch.clamp(pos_pairs.mean() - neg_pairs.mean() + self.margin, min=0.0)

        else:  # 'all' mining
            # Use all pairs
            pos_loss = distances.masked_select(mask_pos).mean()
            neg_loss = torch.clamp(self.margin - distances.masked_select(mask_neg), min=0.0).mean()
            loss = pos_loss + neg_loss

        return loss