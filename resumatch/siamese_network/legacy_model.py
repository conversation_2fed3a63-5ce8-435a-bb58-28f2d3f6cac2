import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple
import os
import json
from datetime import datetime

class LegacySiameseNetwork(nn.Module):
    """
    Legacy Siamese neural network for matching resume embeddings with job description embeddings.
    This class is designed to be compatible with older saved model weights.
    """

    def __init__(self, input_dim, hidden_dim=256, output_dim=128):
        """
        Initialize the Legacy Siamese Network with architecture matching the saved weights

        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
        """
        super(LegacySiameseNetwork, self).__init__()

        # Define network architecture to exactly match the saved model
        # Based on the error messages, the saved model has this structure:
        # encoder.0.weight - Linear layer (384 -> 256)
        # encoder.2.weight, bias, running_mean, running_var, num_batches_tracked - BatchNorm
        # encoder.4.weight, bias - Linear layer (256 -> 128)
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),  # encoder.0
            nn.ReLU(),                         # encoder.1
            nn.BatchNorm1d(hidden_dim),        # encoder.2
            nn.Dropout(0.2),                   # encoder.3
            nn.Linear(hidden_dim, output_dim), # encoder.4
        )

    def forward(self, x):
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)

        # Handle batch normalization for single samples
        if x.size(0) == 1:
            # Use eval mode for batch norm with single sample
            training = self.training
            self.eval()
            with torch.no_grad():
                output = self.encoder(x)
            self.train(training)
            return output
        else:
            return self.encoder(x)

    def forward_pair(self, x1, x2):
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2


class LegacyResumeMatcher:
    """
    Class to match resumes with job descriptions using a Legacy Siamese network.
    This class is designed to be compatible with older saved model weights.
    """

    def __init__(self, embedding_dim, model_path=None):
        """
        Initialize the LegacyResumeMatcher

        Args:
            embedding_dim (int): Dimension of input embeddings
            model_path (str, optional): Path to pre-trained model weights
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # For the legacy model, we need to use the exact dimensions from the saved model
        # The saved model expects input_dim=384, but our current embeddings are 300
        # We'll pad the embeddings to match
        self.input_dim = 384  # Fixed to match saved model
        self.embedding_dim = embedding_dim  # Actual embedding dimension

        self.model = LegacySiameseNetwork(self.input_dim, hidden_dim=256, output_dim=128).to(self.device)

        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()

    def match(self, resume_embedding, job_embeddings_list, similarity_threshold=0.7):
        """
        Match a resume with multiple job descriptions

        Args:
            resume_embedding (np.ndarray): Embedding of a resume
            job_embeddings_list (List[np.ndarray]): List of job description embeddings
            similarity_threshold (float): Threshold for considering a match

        Returns:
            List[Tuple[int, float]]: List of (job_index, similarity_score) sorted by similarity
        """
        self.model.eval()

        # Pad the resume embedding to match the expected input dimension
        padded_resume_embedding = self._pad_embedding(resume_embedding)

        # Convert resume embedding to tensor
        resume_tensor = torch.tensor(padded_resume_embedding, dtype=torch.float32).to(self.device)

        matches = []

        with torch.no_grad():
            # Encode resume
            resume_encoded = self.model(resume_tensor)

            # Process each job embedding
            for i, job_embedding in enumerate(job_embeddings_list):
                # Pad the job embedding
                padded_job_embedding = self._pad_embedding(job_embedding)

                # Convert job embedding to tensor
                job_tensor = torch.tensor(padded_job_embedding, dtype=torch.float32).to(self.device)

                # Encode job
                job_encoded = self.model(job_tensor)

                # Calculate cosine similarity
                similarity = self._cosine_similarity(resume_encoded, job_encoded).item()

                if similarity >= similarity_threshold:
                    matches.append((i, similarity))

        # Sort matches by similarity score (descending)
        matches.sort(key=lambda x: x[1], reverse=True)

        return matches

    def _pad_embedding(self, embedding):
        """
        Pad embedding to match the expected input dimension of the legacy model

        Args:
            embedding (np.ndarray): Original embedding

        Returns:
            np.ndarray: Padded embedding
        """
        # Handle case where embedding is a 2D array
        if isinstance(embedding, np.ndarray) and embedding.ndim > 1:
            # If it's a batch of embeddings, process the first one
            embedding = embedding[0]

        if len(embedding) >= self.input_dim:
            # If embedding is already large enough, truncate it
            return embedding[:self.input_dim]
        else:
            # Pad with zeros
            padded = np.zeros(self.input_dim)
            padded[:len(embedding)] = embedding
            return padded

    def _cosine_similarity(self, x1, x2):
        """Calculate cosine similarity between two vectors"""
        return torch.nn.functional.cosine_similarity(x1, x2, dim=1)
