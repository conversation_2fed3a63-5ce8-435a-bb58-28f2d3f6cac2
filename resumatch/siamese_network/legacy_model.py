import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import re
import os
import json
from typing import Dict, List, Tuple
from datetime import datetime

class LegacySiameseNetwork(nn.Module):
    """
    Legacy Siamese neural network for matching resume embeddings with job description embeddings.
    This class is designed to be compatible with older saved model weights.
    """

    def __init__(self, input_dim, hidden_dim=256, output_dim=128):
        """
        Initialize the Legacy Siamese Network with architecture matching the saved weights

        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
        """
        super(LegacySiameseNetwork, self).__init__()

        # Define network architecture to exactly match the saved model
        # Based on the error messages, the saved model has this structure:
        # encoder.0.weight - Linear layer (384 -> 256)
        # encoder.2.weight, bias, running_mean, running_var, num_batches_tracked - BatchNorm
        # encoder.4.weight, bias - Linear layer (256 -> 128)
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),  # encoder.0
            nn.ReLU(),                         # encoder.1
            nn.BatchNorm1d(hidden_dim),        # encoder.2
            nn.Dropout(0.2),                   # encoder.3
            nn.Linear(hidden_dim, output_dim), # encoder.4
        )

    def forward(self, x):
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)

        # Handle batch normalization for single samples
        if x.size(0) == 1:
            # Use eval mode for batch norm with single sample
            training = self.training
            self.eval()
            with torch.no_grad():
                output = self.encoder(x)
            self.train(training)
            return output
        else:
            return self.encoder(x)

    def forward_pair(self, x1, x2):
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2


class LegacyResumeMatcher:
    """
    Class to match resumes with job descriptions using a Legacy Siamese network.
    This class is designed to be compatible with older saved model weights.
    """

    def __init__(self, embedding_dim, model_path=None):
        """
        Initialize the LegacyResumeMatcher

        Args:
            embedding_dim (int): Dimension of input embeddings
            model_path (str, optional): Path to pre-trained model weights
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # For the legacy model, we need to use the exact dimensions from the saved model
        # The saved model expects input_dim=384, but our current embeddings are 300
        # We'll pad the embeddings to match
        self.input_dim = 384  # Fixed to match saved model
        self.embedding_dim = embedding_dim  # Actual embedding dimension

        self.model = LegacySiameseNetwork(self.input_dim, hidden_dim=256, output_dim=128).to(self.device)

        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()

    def match(self, resume_embedding, job_embeddings_list, similarity_threshold=0.7):
        """
        Match a resume with multiple job descriptions

        Args:
            resume_embedding (np.ndarray): Embedding of a resume
            job_embeddings_list (List[np.ndarray]): List of job description embeddings
            similarity_threshold (float): Threshold for considering a match

        Returns:
            List[Tuple[int, float]]: List of (job_index, similarity_score) sorted by similarity
        """
        self.model.eval()

        # Pad the resume embedding to match the expected input dimension
        padded_resume_embedding = self._pad_embedding(resume_embedding)

        # Convert resume embedding to tensor
        resume_tensor = torch.tensor(padded_resume_embedding, dtype=torch.float32).to(self.device)

        matches = []

        with torch.no_grad():
            # Encode resume
            resume_encoded = self.model(resume_tensor)

            # Process each job embedding
            for i, job_embedding in enumerate(job_embeddings_list):
                # Pad the job embedding
                padded_job_embedding = self._pad_embedding(job_embedding)

                # Convert job embedding to tensor
                job_tensor = torch.tensor(padded_job_embedding, dtype=torch.float32).to(self.device)

                # Encode job
                job_encoded = self.model(job_tensor)

                # Calculate cosine similarity
                similarity = self._cosine_similarity(resume_encoded, job_encoded).item()

                if similarity >= similarity_threshold:
                    matches.append((i, similarity))

        # Sort matches by similarity score (descending)
        matches.sort(key=lambda x: x[1], reverse=True)

        return matches

    def _pad_embedding(self, embedding):
        """
        Pad embedding to match the expected input dimension of the legacy model

        Args:
            embedding (np.ndarray): Original embedding

        Returns:
            np.ndarray: Padded embedding
        """
        # Handle case where embedding is a 2D array
        if isinstance(embedding, np.ndarray) and embedding.ndim > 1:
            # If it's a batch of embeddings, process the first one
            embedding = embedding[0]

        if len(embedding) >= self.input_dim:
            # If embedding is already large enough, truncate it
            return embedding[:self.input_dim]
        else:
            # Pad with zeros
            padded = np.zeros(self.input_dim)
            padded[:len(embedding)] = embedding
            return padded

    def _cosine_similarity(self, x1, x2):
        """Calculate cosine similarity between two vectors"""
        return torch.nn.functional.cosine_similarity(x1, x2, dim=1)

    def get_similarity(self, resume_embedding, job_embedding, resume_text=None, job_text=None):
        """
        Calculate enhanced similarity between a resume and job embedding with skill-aware scoring

        Args:
            resume_embedding (np.ndarray): Resume embedding vector
            job_embedding (np.ndarray): Job embedding vector
            resume_text (str, optional): Resume text for skill analysis
            job_text (str, optional): Job text for skill analysis

        Returns:
            float: Similarity score between 0 and 1
        """
        self.model.eval()

        # Pad embeddings to match expected input dimension
        padded_resume_embedding = self._pad_embedding(resume_embedding)
        padded_job_embedding = self._pad_embedding(job_embedding)

        # Convert embeddings to tensors
        resume_tensor = torch.tensor(padded_resume_embedding, dtype=torch.float32).to(self.device)
        job_tensor = torch.tensor(padded_job_embedding, dtype=torch.float32).to(self.device)

        with torch.no_grad():
            try:
                # Calculate base similarity using embeddings
                base_similarity = self._calculate_base_similarity(resume_tensor, job_tensor, resume_embedding, job_embedding)

                # If we have text data, enhance with skill-aware scoring
                if resume_text and job_text:
                    enhanced_similarity = self._calculate_enhanced_similarity(base_similarity, resume_text, job_text)
                    return enhanced_similarity
                else:
                    # Apply improved scaling for embedding-only similarity
                    return self._apply_improved_scaling(base_similarity)

            except Exception as e:
                # Fallback to direct cosine similarity if anything fails
                return self._calculate_direct_cosine_similarity(resume_embedding, job_embedding)

    def _calculate_base_similarity(self, resume_tensor, job_tensor, resume_embedding, job_embedding):
        """Calculate base similarity using both Siamese network and direct cosine similarity"""
        try:
            # Encode both embeddings using Siamese network
            resume_encoded = self.model(resume_tensor)
            job_encoded = self.model(job_tensor)

            # Calculate cosine similarity from Siamese network
            siamese_similarity = self._cosine_similarity(resume_encoded, job_encoded).item()
        except:
            siamese_similarity = 0.5  # Neutral fallback

        # Calculate direct cosine similarity between original embeddings
        direct_similarity = self._calculate_direct_cosine_similarity(resume_embedding, job_embedding)

        # Combine similarities with adjusted weights
        # Reduce Siamese network weight since it's not well-trained for our specific use case
        combined_similarity = 0.2 * siamese_similarity + 0.8 * direct_similarity

        return combined_similarity

    def _calculate_direct_cosine_similarity(self, embedding1, embedding2):
        """Calculate direct cosine similarity between two embeddings"""
        # Ensure embeddings are numpy arrays
        if isinstance(embedding1, torch.Tensor):
            embedding1 = embedding1.cpu().numpy()
        if isinstance(embedding2, torch.Tensor):
            embedding2 = embedding2.cpu().numpy()

        # Flatten if needed
        embedding1 = embedding1.flatten()
        embedding2 = embedding2.flatten()

        # Calculate cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)

        # Ensure similarity is between 0 and 1
        similarity = max(0.0, min(1.0, (similarity + 1) / 2))  # Convert from [-1,1] to [0,1]

        return similarity

    def _scale_similarity(self, similarity):
        """Scale similarity to provide more realistic and varied scores"""
        # Apply a sigmoid-like transformation to spread out scores
        import math

        # Convert to a range that gives more realistic job matching scores
        # Most job matches should be in the 0.3-0.9 range, not 0.95-0.99

        # Apply a power transformation to reduce very high similarities
        scaled = math.pow(similarity, 1.5)

        # Apply additional scaling to get more realistic ranges
        # Map [0,1] to approximately [0.2, 0.95] with most values in [0.3, 0.8]
        final_score = 0.2 + (scaled * 0.75)

        return min(0.95, max(0.15, final_score))

    def _calculate_enhanced_similarity(self, base_similarity, resume_text, job_text):
        """Calculate enhanced similarity using skill matching and domain analysis"""

        # Extract key skills and technologies from both texts
        resume_skills = self._extract_key_skills(resume_text.lower())
        job_skills = self._extract_key_skills(job_text.lower())

        # Calculate skill overlap score
        skill_overlap_score = self._calculate_skill_overlap(resume_skills, job_skills)

        # Calculate domain relevance score
        domain_score = self._calculate_domain_relevance(resume_text.lower(), job_text.lower())

        # Calculate experience level alignment
        experience_score = self._calculate_experience_alignment(resume_text.lower(), job_text.lower())

        # Combine all scores with weights
        # Increase importance of skill overlap and domain relevance for better differentiation
        # Base similarity: 30%, Skill overlap: 45%, Domain relevance: 20%, Experience: 5%
        enhanced_similarity = (
            0.30 * base_similarity +
            0.45 * skill_overlap_score +
            0.20 * domain_score +
            0.05 * experience_score
        )

        # Apply final scaling to ensure proper score distribution
        return self._apply_enhanced_scaling(enhanced_similarity, skill_overlap_score, domain_score)

    def _extract_key_skills(self, text):
        """Extract key technical skills from text"""
        # Define comprehensive skill categories with weights
        skill_patterns = {
            # High-value mobile/app development skills
            'mobile_dev': ['flutter', 'dart', 'android', 'ios', 'react native', 'xamarin', 'ionic', 'cordova'],
            'mobile_frameworks': ['riverpod', 'bloc', 'provider', 'getx', 'redux'],
            'mobile_tools': ['android studio', 'xcode', 'firebase', 'play store', 'app store'],

            # Programming languages
            'languages': ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust', 'swift', 'kotlin'],

            # Web technologies
            'web_frontend': ['react', 'angular', 'vue', 'html', 'css', 'bootstrap', 'tailwind'],
            'web_backend': ['fastapi', 'django', 'flask', 'express', 'spring', 'asp.net'],

            # Databases
            'databases': ['mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'firestore'],

            # Cloud and DevOps
            'cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins'],

            # APIs and protocols
            'apis': ['rest', 'graphql', 'websocket', 'json', 'xml', 'grpc'],

            # Data science
            'data_science': ['tensorflow', 'pytorch', 'pandas', 'numpy', 'scikit-learn', 'matplotlib'],

            # Other technical
            'other_tech': ['git', 'github', 'linux', 'sql', 'nosql', 'microservices']
        }

        found_skills = {}
        for category, skills in skill_patterns.items():
            found_skills[category] = []
            for skill in skills:
                if re.search(r'\b' + re.escape(skill) + r'\b', text):
                    found_skills[category].append(skill)

        return found_skills

    def _calculate_skill_overlap(self, resume_skills, job_skills):
        """Calculate skill overlap score with category weighting"""
        # Define category weights based on importance for matching
        category_weights = {
            'mobile_dev': 3.0,      # Highest weight for mobile development
            'mobile_frameworks': 2.5,
            'mobile_tools': 2.0,
            'languages': 2.0,
            'web_frontend': 1.5,
            'web_backend': 1.8,
            'databases': 1.3,
            'cloud': 1.2,
            'apis': 1.5,
            'data_science': 1.0,
            'other_tech': 1.0
        }

        total_weighted_overlap = 0
        total_weighted_job_skills = 0

        for category in resume_skills.keys():
            resume_cat_skills = set(resume_skills[category])
            job_cat_skills = set(job_skills.get(category, []))

            if job_cat_skills:  # Only consider categories that the job requires
                overlap = len(resume_cat_skills.intersection(job_cat_skills))
                total_job_skills = len(job_cat_skills)

                weight = category_weights.get(category, 1.0)

                total_weighted_overlap += overlap * weight
                total_weighted_job_skills += total_job_skills * weight

        if total_weighted_job_skills == 0:
            return 0.3  # Low score if no technical skills found in job

        skill_score = total_weighted_overlap / total_weighted_job_skills

        # Apply additional penalties for missing high-importance skills
        # Check if job requires mobile development but resume doesn't have it
        job_mobile_skills = set(job_skills.get('mobile_dev', []) + job_skills.get('mobile_frameworks', []))
        resume_mobile_skills = set(resume_skills.get('mobile_dev', []) + resume_skills.get('mobile_frameworks', []))

        if job_mobile_skills and not resume_mobile_skills:
            # Job requires mobile skills but resume doesn't have them
            skill_score *= 0.4  # Strong penalty

        # Check for programming language mismatches
        job_languages = set(job_skills.get('languages', []))
        resume_languages = set(resume_skills.get('languages', []))

        if job_languages and not resume_languages.intersection(job_languages):
            # No programming language overlap
            skill_score *= 0.6  # Moderate penalty

        # Additional penalties for domain-specific skill mismatches
        # Penalize data science jobs if resume doesn't have data science skills
        job_data_skills = set(job_skills.get('data_science', []))
        resume_data_skills = set(resume_skills.get('data_science', []))

        if job_data_skills and len(resume_data_skills) == 0:
            # Job requires data science but resume doesn't have it
            skill_score *= 0.5  # Strong penalty for data science jobs

        # Penalize DevOps jobs if resume doesn't have strong DevOps skills
        job_cloud_skills = set(job_skills.get('cloud', []))
        resume_cloud_skills = set(resume_skills.get('cloud', []))

        if job_cloud_skills and len(resume_cloud_skills) <= 1:
            # Job requires cloud/DevOps but resume has minimal skills
            skill_score *= 0.6  # Moderate penalty for DevOps jobs

        return min(1.0, skill_score)  # Cap at 1.0

    def _calculate_domain_relevance(self, resume_text, job_text):
        """Calculate domain relevance score"""
        # Define domain keywords with weights
        domain_keywords = {
            'mobile_app': ['mobile', 'app', 'application', 'smartphone', 'tablet'],
            'web_dev': ['web', 'website', 'frontend', 'backend', 'fullstack', 'full stack'],
            'data_science': ['data science', 'machine learning', 'ai', 'analytics', 'big data'],
            'devops': ['devops', 'infrastructure', 'deployment', 'ci/cd', 'automation'],
            'design': ['ui', 'ux', 'design', 'user interface', 'user experience'],
            'business': ['marketing', 'sales', 'business', 'management', 'strategy'],
            'finance': ['accounting', 'finance', 'financial', 'budget', 'audit'],
            'engineering': ['mechanical', 'electrical', 'civil', 'manufacturing']
        }

        resume_domains = set()
        job_domains = set()

        for domain, keywords in domain_keywords.items():
            for keyword in keywords:
                if re.search(r'\b' + re.escape(keyword) + r'\b', resume_text):
                    resume_domains.add(domain)
                if re.search(r'\b' + re.escape(keyword) + r'\b', job_text):
                    job_domains.add(domain)

        if not job_domains:
            return 0.5  # Neutral if no clear domain

        overlap = len(resume_domains.intersection(job_domains))
        total_job_domains = len(job_domains)

        # Calculate base overlap score
        if total_job_domains == 0:
            return 0.5

        overlap_score = overlap / total_job_domains

        # Apply penalties for clear domain mismatches
        # If job is clearly in a different domain (business, finance, engineering) and resume is tech-focused
        tech_domains = {'mobile_app', 'web_dev', 'data_science', 'devops', 'design'}
        non_tech_domains = {'business', 'finance', 'engineering'}

        resume_is_tech = bool(resume_domains.intersection(tech_domains))
        job_is_non_tech = bool(job_domains.intersection(non_tech_domains))

        if resume_is_tech and job_is_non_tech and overlap == 0:
            # Strong penalty for tech resume vs non-tech job
            return max(0.1, overlap_score * 0.3)

        return overlap_score

    def _calculate_experience_alignment(self, resume_text, job_text):
        """Calculate experience level alignment"""
        # Extract experience indicators
        resume_exp_indicators = []
        job_exp_indicators = []

        # Experience patterns
        exp_patterns = {
            'entry': ['entry', 'junior', 'graduate', 'intern', '0-2 years', '1-2 years'],
            'mid': ['mid', 'intermediate', '2-5 years', '3-5 years', '2+ years'],
            'senior': ['senior', 'lead', 'principal', '5+ years', '7+ years', 'expert']
        }

        for level, patterns in exp_patterns.items():
            for pattern in patterns:
                if re.search(r'\b' + re.escape(pattern) + r'\b', resume_text):
                    resume_exp_indicators.append(level)
                if re.search(r'\b' + re.escape(pattern) + r'\b', job_text):
                    job_exp_indicators.append(level)

        # If no clear indicators, return neutral
        if not resume_exp_indicators or not job_exp_indicators:
            return 0.6

        # Check for alignment
        if any(level in job_exp_indicators for level in resume_exp_indicators):
            return 0.8
        else:
            return 0.4

    def _apply_enhanced_scaling(self, similarity, skill_overlap, domain_score):
        """Apply enhanced scaling based on skill overlap and domain relevance"""
        import math

        # More aggressive penalties for low relevance
        if skill_overlap < 0.1 and domain_score < 0.2:
            return min(0.25, similarity * 0.5)  # Very low cap for irrelevant jobs

        if skill_overlap < 0.2 and domain_score < 0.4:
            return min(0.35, similarity * 0.7)  # Low cap for poor matches

        # Medium penalties for partial matches
        if skill_overlap < 0.4 or domain_score < 0.5:
            similarity *= 0.85  # Moderate penalty

        # Boost high-relevance matches
        if skill_overlap > 0.7 and domain_score > 0.8:
            similarity *= 1.15  # Boost excellent matches
        elif skill_overlap > 0.5 and domain_score > 0.6:
            similarity *= 1.05  # Small boost for good matches

        # Apply sigmoid-like scaling for better distribution
        scaled = 1 / (1 + math.exp(-8 * (similarity - 0.5)))

        # Map to desired range with more spread: 0.15 to 0.90
        final_score = 0.15 + (scaled * 0.75)

        return min(0.90, max(0.15, final_score))

    def _apply_improved_scaling(self, similarity):
        """Apply improved scaling for embedding-only similarity"""
        import math

        # Apply more aggressive scaling to spread out scores
        # Use a power function to reduce high similarities and boost low ones
        if similarity > 0.7:
            # Reduce very high similarities
            scaled = 0.7 + (similarity - 0.7) * 0.5
        else:
            # Keep lower similarities more linear
            scaled = similarity

        # Apply sigmoid transformation for better distribution
        sigmoid_scaled = 1 / (1 + math.exp(-8 * (scaled - 0.5)))

        # Map to range 0.2 to 0.85
        final_score = 0.2 + (sigmoid_scaled * 0.65)

        return min(0.85, max(0.20, final_score))
