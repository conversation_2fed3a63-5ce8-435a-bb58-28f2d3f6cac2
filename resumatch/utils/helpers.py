import os
import json
import torch
import numpy as np
from typing import Dict, <PERSON>, Tuple, Any, Optional
from datetime import datetime
import re
import random

def setup_directories(base_path: str) -> Dict[str, str]:
    """
    Create the necessary directories for the application
    
    Args:
        base_path (str): Base path for the application
        
    Returns:
        Dict[str, str]: Dictionary of directory paths
    """
    directories = {
        'models': os.path.join(base_path, 'models'),
        'data': os.path.join(base_path, 'data', 'datasets'),
        'embeddings': os.path.join(base_path, 'data', 'embeddings'),
        'uploads': os.path.join(base_path, 'data', 'uploads'),
        'logs': os.path.join(base_path, 'logs'),
    }
    
    # Create directories if they don't exist
    for dir_path in directories.values():
        os.makedirs(dir_path, exist_ok=True)
    
    return directories

def calculate_similarity(vec1: np.ndarray, vec2: np.ndarray) -> float:
    """
    Calculate cosine similarity between two vectors
    
    Args:
        vec1 (np.ndarray): First vector
        vec2 (np.ndarray): Second vector
        
    Returns:
        float: Cosine similarity (0-1)
    """
    # Handle edge cases
    if len(vec1) == 0 or len(vec2) == 0:
        return 0.0
        
    # Calculate cosine similarity
    dot_product = np.dot(vec1, vec2)
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)
    
    # Avoid division by zero
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0.0
        
    return dot_product / (norm_vec1 * norm_vec2)

def create_training_pairs(resume_data: List[Dict], job_data: List[Dict], 
                          num_positive: int = 100, num_negative: int = 300) -> Tuple[List, List, List]:
    """
    Create training pairs for the Siamese network
    
    Args:
        resume_data (List[Dict]): List of resume data
        job_data (List[Dict]): List of job data
        num_positive (int): Number of positive pairs to create
        num_negative (int): Number of negative pairs to create
        
    Returns:
        Tuple[List, List, List]: (resume_ids, job_ids, labels)
    """
    resume_ids = []
    job_ids = []
    labels = []
    
    # Create positive pairs (similar resumes and jobs)
    # For demonstration, we'll use a simple heuristic to create "matching" pairs
    for _ in range(num_positive):
        resume_idx = random.randint(0, len(resume_data) - 1)
        
        # Find a job that has overlapping skills with the resume
        resume = resume_data[resume_idx]
        resume_skills = set([skill.lower() for skill in resume.get('entities', {}).get('skills', [])])
        
        # If no skills found, choose random job
        if not resume_skills:
            job_idx = random.randint(0, len(job_data) - 1)
        else:
            # Try to find a job with matching skills
            matching_jobs = []
            for j_idx, job in enumerate(job_data):
                job_desc = job.get('description', '').lower()
                if any(skill in job_desc for skill in resume_skills):
                    matching_jobs.append(j_idx)
            
            # If no matching jobs found, choose random job
            if not matching_jobs:
                job_idx = random.randint(0, len(job_data) - 1)
            else:
                job_idx = random.choice(matching_jobs)
        
        resume_ids.append(resume['resume_id'])
        job_ids.append(job_data[job_idx]['job_id'])
        labels.append(1)  # 1 for match
    
    # Create negative pairs (dissimilar resumes and jobs)
    for _ in range(num_negative):
        resume_idx = random.randint(0, len(resume_data) - 1)
        job_idx = random.randint(0, len(job_data) - 1)
        
        resume_ids.append(resume_data[resume_idx]['resume_id'])
        job_ids.append(job_data[job_idx]['job_id'])
        labels.append(0)  # 0 for non-match
    
    return resume_ids, job_ids, labels

def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    Extract important keywords from text using simple frequency analysis
    
    Args:
        text (str): Input text
        max_keywords (int): Maximum number of keywords to extract
        
    Returns:
        List[str]: List of extracted keywords
    """
    # Common stopwords to filter out
    stopwords = {
        'a', 'an', 'the', 'and', 'or', 'but', 'if', 'because', 'as', 'what',
        'when', 'where', 'how', 'who', 'which', 'this', 'that', 'to', 'in',
        'of', 'for', 'with', 'by', 'at', 'on', 'from', 'be', 'is', 'are',
        'was', 'were', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
        'will', 'would', 'should', 'can', 'could'
    }
    
    # Clean and tokenize text
    text = re.sub(r'[^\w\s]', ' ', text.lower())
    words = text.split()
    
    # Remove stopwords and short words
    filtered_words = [word for word in words if word not in stopwords and len(word) > 2]
    
    # Count word frequencies
    word_freq = {}
    for word in filtered_words:
        if word in word_freq:
            word_freq[word] += 1
        else:
            word_freq[word] = 1
    
    # Sort by frequency and get top keywords
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, _ in sorted_words[:max_keywords]]
    
    return keywords

def log_event(log_file: str, event_type: str, message: str) -> None:
    """
    Log an event to a file
    
    Args:
        log_file (str): Path to log file
        event_type (str): Type of event (e.g., 'INFO', 'ERROR')
        message (str): Log message
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] [{event_type}] {message}\n"
    
    try:
        with open(log_file, 'a') as f:
            f.write(log_entry)
    except Exception as e:
        print(f"Error writing to log file: {e}")
        print(log_entry)