import os
import sys
import pandas as pd
import json
from pathlib import Path
import opendatasets as od

# Add parent directory to sys path to allow imports
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from resumatch.utils.helpers import setup_directories, log_event


def download_resume_dataset(dataset_url="https://www.kaggle.com/datasets/snehaanbhawal/resume-dataset"):
    """
    Download the resume dataset from Kaggle

    Args:
        dataset_url: URL to the Kaggle dataset

    Returns:
        Path to downloaded dataset
    """
    # Set up directories
    base_path = Path(__file__).resolve().parent.parent.parent
    directories = setup_directories(base_path)
    dataset_dir = os.path.join(directories['data'], 'datasets', 'resume_dataset')

    log_file = os.path.join(directories['logs'], 'dataset.log')
    log_event(log_file, "INFO", f"Downloading dataset from {dataset_url}")

    # Create dataset directory if it doesn't exist
    os.makedirs(dataset_dir, exist_ok=True)

    # Clean up any existing nested directories
    print("Cleaning up any existing nested directories...")
    if os.path.exists(dataset_dir):
        for item in os.listdir(dataset_dir):
            item_path = os.path.join(dataset_dir, item)
            if os.path.isdir(item_path) and item == "resume-dataset":
                print(f"Found nested directory: {item_path}")
                # Move files from nested directory to parent
                for nested_item in os.listdir(item_path):
                    nested_item_path = os.path.join(item_path, nested_item)
                    target_path = os.path.join(dataset_dir, nested_item)
                    print(f"Moving {nested_item_path} to {target_path}")
                    if os.path.exists(target_path):
                        if os.path.isdir(target_path):
                            import shutil
                            shutil.rmtree(target_path)
                        else:
                            os.remove(target_path)
                    if os.path.isdir(nested_item_path):
                        import shutil
                        shutil.copytree(nested_item_path, target_path)
                    else:
                        import shutil
                        shutil.copy2(nested_item_path, target_path)

    # Download dataset
    print(f"Downloading resume dataset from {dataset_url}")
    try:
        print("Using opendatasets to download. You may need to enter your Kaggle credentials.")
        od.download(dataset_url, dataset_dir)

        # Check if download was successful
        if os.path.exists(dataset_dir):
            print(f"Dataset directory created at: {dataset_dir}")
            files = os.listdir(dataset_dir)
            print(f"Files in dataset directory: {files}")

            log_event(log_file, "INFO", f"Dataset downloaded to {dataset_dir}")
            return dataset_dir
        else:
            print(f"Dataset directory not found after download: {dataset_dir}")
            log_event(log_file, "ERROR", f"Dataset directory not found after download: {dataset_dir}")
            return None
    except Exception as e:
        log_event(log_file, "ERROR", f"Failed to download dataset: {str(e)}")
        print(f"Error downloading dataset: {e}")
        print("Make sure you have a Kaggle account and have set up your API credentials.")
        print("Visit https://www.kaggle.com/account and create an API token, then place kaggle.json in ~/.kaggle/")
        return None


def process_resume_dataset(dataset_path):
    """
    Process the downloaded resume dataset and convert to our project's format

    Args:
        dataset_path: Path to the downloaded dataset

    Returns:
        Path to the processed dataset JSON file
    """
    base_path = Path(__file__).resolve().parent.parent.parent
    directories = setup_directories(base_path)
    log_file = os.path.join(directories['logs'], 'dataset.log')

    # Find the CSV file
    print(f"Looking for CSV files in: {dataset_path}")
    if not os.path.exists(dataset_path):
        print(f"Error: Dataset path does not exist: {dataset_path}")
        log_event(log_file, "ERROR", f"Dataset path does not exist: {dataset_path}")
        return None

    # List all files in the directory
    all_files = os.listdir(dataset_path)
    print(f"All files in dataset directory: {all_files}")

    # Look for CSV files
    csv_files = list(Path(dataset_path).glob("*.csv"))
    print(f"CSV files found: {csv_files}")

    if not csv_files:
        print("Error: No CSV file found in the dataset directory")
        log_event(log_file, "ERROR", "No CSV file found in the dataset directory")

        # Try looking in subdirectories
        print("Searching in subdirectories...")
        for subdir in os.listdir(dataset_path):
            subdir_path = os.path.join(dataset_path, subdir)
            if os.path.isdir(subdir_path):
                print(f"Checking subdirectory: {subdir_path}")
                subdir_csv_files = list(Path(subdir_path).glob("*.csv"))
                if subdir_csv_files:
                    print(f"Found CSV files in subdirectory: {subdir_csv_files}")
                    csv_files = subdir_csv_files
                    break

                # Look one level deeper
                for subsubdir in os.listdir(subdir_path):
                    subsubdir_path = os.path.join(subdir_path, subsubdir)
                    if os.path.isdir(subsubdir_path):
                        print(f"Checking nested subdirectory: {subsubdir_path}")
                        subsubdir_csv_files = list(Path(subsubdir_path).glob("*.csv"))
                        if subsubdir_csv_files:
                            print(f"Found CSV files in nested subdirectory: {subsubdir_csv_files}")
                            csv_files = subsubdir_csv_files
                            break

                if csv_files:
                    break

        if not csv_files:
            return None

    csv_file = csv_files[0]
    print(f"Using CSV file: {csv_file}")
    log_event(log_file, "INFO", f"Processing CSV file: {csv_file}")

    try:
        # Read the dataset
        print(f"Reading CSV file: {csv_file}")
        df = pd.read_csv(csv_file)
        print(f"CSV file loaded. Shape: {df.shape}")
        print(f"CSV columns: {df.columns.tolist()}")

        # Check if required columns exist
        if 'Resume' not in df.columns:
            print(f"Error: 'Resume' column not found in CSV. Available columns: {df.columns.tolist()}")
            log_event(log_file, "ERROR", f"'Resume' column not found in CSV")

            # Try to find a suitable column
            text_columns = [col for col in df.columns if 'text' in col.lower() or 'resume' in col.lower()]
            if text_columns:
                print(f"Using alternative column for resume text: {text_columns[0]}")
                df['Resume'] = df[text_columns[0]]
            else:
                return None

        if 'Category' not in df.columns:
            print(f"Warning: 'Category' column not found in CSV. Using 'Unknown' as default.")
            df['Category'] = 'Unknown'

        # Transform to our format
        resumes = []
        print("Processing resume data...")
        for idx, row in df.iterrows():
            if idx % 100 == 0:
                print(f"Processing resume {idx}/{len(df)}...")

            resume_id = f"resume_{idx}"

            # Extract skills from resume text
            resume_text = str(row.get('Resume', ''))
            category = str(row.get('Category', 'Unknown'))

            # Create structured resume data
            resume_data = {
                'resume_id': resume_id,
                'category': category,
                'text': resume_text,
                'entities': {
                    'skills': extract_skills_from_text(resume_text),
                    'experience': extract_experience_from_text(resume_text)
                }
            }
            resumes.append(resume_data)

        # Save as JSON
        output_file = os.path.join(directories['data'], 'processed_resumes.json')
        print(f"Saving processed data to: {output_file}")
        with open(output_file, 'w') as f:
            json.dump(resumes, f, indent=2)

        print(f"Successfully processed {len(resumes)} resumes and saved to {output_file}")
        log_event(log_file, "INFO", f"Processed {len(resumes)} resumes and saved to {output_file}")
        return output_file

    except Exception as e:
        log_event(log_file, "ERROR", f"Failed to process dataset: {str(e)}")
        print(f"Error processing dataset: {e}")
        import traceback
        traceback.print_exc()
        return None


def extract_skills_from_text(text):
    """
    Extract skills from resume text (simplified version)

    Args:
        text: Resume text

    Returns:
        List of skills
    """
    # List of common tech skills to look for
    common_skills = [
        "python", "java", "javascript", "html", "css", "react", "node.js", "sql",
        "aws", "azure", "docker", "kubernetes", "git", "agile", "scrum", "excel",
        "powerpoint", "word", "project management", "leadership", "communication",
        "machine learning", "data analysis", "ai", "tensorflow", "pytorch", "nlp",
        "pandas", "numpy", "django", "flask", "spring", "angular", "vue", "php"
    ]

    # Extract skills that appear in the text
    skills = []
    for skill in common_skills:
        if skill.lower() in text.lower():
            skills.append(skill)

    return skills


def extract_experience_from_text(text):
    """
    Extract experience from resume text (simplified version)

    Args:
        text: Resume text

    Returns:
        List of experiences
    """
    # This is a simplified implementation - in a real system you'd use NLP techniques
    # to extract structured experience information

    # For now, we'll just return lines that might represent experience
    lines = text.split('\n')
    experiences = []
    for line in lines:
        if len(line) > 40:  # Arbitrary length to catch description lines
            experiences.append(line.strip())

    return experiences[:3]  # Just take first 3 possible experiences


if __name__ == "__main__":
    print("=== ResuMatch Dataset Downloader ===")

    # Download the dataset
    dataset_path = download_resume_dataset()
    if dataset_path:
        # Process the dataset
        output_path = process_resume_dataset(dataset_path)
        if output_path:
            print(f"Dataset successfully downloaded and processed to: {output_path}")
            print("You can now use this data with ResuMatch!")
        else:
            print("Failed to process the dataset.")
    else:
        print("Failed to download the dataset.")