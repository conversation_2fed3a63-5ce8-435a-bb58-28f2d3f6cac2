import numpy as np
from typing import List, Union
import re
from collections import Counter

class TextEmbedder:
    """
    Simple text embedder class that uses TF-IDF like approach for embeddings.
    This is a fallback implementation that doesn't rely on external ML libraries.
    """

    def __init__(self, model_name=None):
        """
        Initialize the text embedder with basic configuration

        Args:
            model_name: Ignored, kept for compatibility
        """
        print("Using simple TF-IDF based embedder (no external ML libraries)")
        self.embedding_dim = 300  # Fixed dimension for our simple embeddings
        self.vocabulary = {}  # Will be populated as we process text
        self.idf = {}  # Inverse document frequency
        self.doc_count = 0  # Number of documents processed
        self.model_name = "simple-tfidf"
        self.device = "cpu"

    def get_embeddings(self, texts: Union[str, List[str]], pooling=None) -> np.ndarray:
        """
        Generate embeddings for one or more texts using a simple TF-IDF approach

        Args:
            texts (str or List[str]): Text or list of texts to embed
            pooling: Ignored, kept for compatibility

        Returns:
            np.ndarray: Array of embeddings with shape (n_texts, embedding_dim)
        """
        # Convert single string to list
        if isinstance(texts, str):
            texts = [texts]

        # Clean and preprocess texts
        processed_texts = [self._preprocess_text(text) for text in texts]

        # Update document count
        self.doc_count += len(processed_texts)

        # Calculate term frequencies for each document
        term_freqs = []
        for text in processed_texts:
            # Tokenize text into words
            words = text.lower().split()

            # Count word frequencies
            word_counts = {}
            for word in words:
                if word not in word_counts:
                    word_counts[word] = 0
                word_counts[word] += 1

                # Add word to vocabulary if not already present
                if word not in self.vocabulary:
                    self.vocabulary[word] = len(self.vocabulary)

                # Update document frequency
                if word not in self.idf:
                    self.idf[word] = 0
                self.idf[word] += 1

            term_freqs.append(word_counts)

        # Create embeddings for each document
        embeddings = np.zeros((len(processed_texts), self.embedding_dim))

        for i, word_counts in enumerate(term_freqs):
            # Create a sparse vector for this document
            vec = np.zeros(self.embedding_dim)

            # Fill in the vector with TF-IDF values
            for word, count in word_counts.items():
                # Skip if word is not in vocabulary (shouldn't happen)
                if word not in self.vocabulary:
                    continue

                # Get the word's index in the vocabulary
                word_idx = self.vocabulary[word] % self.embedding_dim

                # Calculate TF-IDF
                tf = count / max(len(word_counts), 1)  # Term frequency
                idf = np.log(self.doc_count / max(self.idf.get(word, 1), 1))  # Inverse document frequency

                # Add to vector
                vec[word_idx] += tf * idf

            # Store the embedding
            embeddings[i] = vec

        # Normalize embeddings to unit length
        embeddings = self._normalize_embeddings(embeddings)

        return embeddings

    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text before embedding

        Args:
            text (str): Input text

        Returns:
            str: Preprocessed text
        """
        # Convert to string if not already
        if not isinstance(text, str):
            text = str(text)

        # Remove excessive whitespace
        text = ' '.join(text.split())

        # Truncate very long texts to avoid memory issues
        if len(text) > 10000:
            text = text[:10000]

        return text

    def _calculate_similarity(self, embedding1, embedding2):
        """
        Calculate cosine similarity between two embeddings

        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector

        Returns:
            float: Cosine similarity score
        """
        # Calculate dot product
        dot_product = np.dot(embedding1, embedding2)

        # Calculate magnitudes
        magnitude1 = np.linalg.norm(embedding1)
        magnitude2 = np.linalg.norm(embedding2)

        # Calculate cosine similarity
        if magnitude1 > 0 and magnitude2 > 0:
            similarity = dot_product / (magnitude1 * magnitude2)
        else:
            similarity = 0.0

        return similarity

    def _normalize_embeddings(self, embeddings: np.ndarray) -> np.ndarray:
        """
        Normalize embeddings to unit length

        Args:
            embeddings (np.ndarray): Input embeddings

        Returns:
            np.ndarray: Normalized embeddings
        """
        # Calculate L2 norm along the embedding dimension
        norms = np.linalg.norm(embeddings, axis=1, keepdims=True)

        # Avoid division by zero
        norms = np.maximum(norms, 1e-9)

        # Normalize
        normalized_embeddings = embeddings / norms

        return normalized_embeddings

    def get_section_embeddings(self, sections: dict) -> dict:
        """
        Generate embeddings for different sections of a resume or job description

        Args:
            sections (dict): Dictionary containing different sections of text

        Returns:
            dict: Dictionary with embeddings for each section
        """
        embeddings = {}
        for section_name, section_text in sections.items():
            if section_text and isinstance(section_text, str):
                embeddings[section_name] = self.get_embeddings(section_text)
            elif isinstance(section_text, list):
                # Join list items (like skills) into a single string for embedding
                if all(isinstance(item, str) for item in section_text):
                    combined_text = ", ".join(section_text)
                    embeddings[section_name] = self.get_embeddings(combined_text)

        return embeddings

    def get_resume_embeddings(self, resume_entities, resume_text=None):
        """
        Generate embeddings for the structured resume data

        Args:
            resume_entities (dict): Dictionary containing resume entities
            resume_text (str, optional): Full text of the resume

        Returns:
            dict: Dictionary with embeddings for resume sections
        """
        # Process different parts of the resume
        sections = {}

        # Skills section
        if 'skills' in resume_entities and resume_entities['skills']:
            skills_text = ", ".join(resume_entities['skills'])
            sections['skills'] = skills_text

        # Education section
        if 'education' in resume_entities and resume_entities['education']:
            education_texts = []
            for edu in resume_entities['education']:
                edu_text = f"{edu.get('degree', '')} {edu.get('institution', '')} {edu.get('year', '')}"
                education_texts.append(edu_text)
            sections['education'] = " ".join(education_texts)

        # Experience section
        if 'experience' in resume_entities and resume_entities['experience']:
            exp_texts = []
            for exp in resume_entities['experience']:
                exp_text = f"{exp.get('title', '')} at {exp.get('organization', '')} {exp.get('dates', '')}"
                exp_texts.append(exp_text)
            sections['experience'] = " ".join(exp_texts)

        # Generate embeddings for each section
        section_embeddings = self.get_section_embeddings(sections)

        # Generate a combined embedding for the entire resume
        all_texts = " ".join(sections.values())
        section_embeddings['full_resume'] = self.get_embeddings(all_texts)

        return section_embeddings

    def get_job_description_embeddings(self, job_description_text):
        """
        Generate embeddings for a job description

        Args:
            job_description_text (str): Text of the job description

        Returns:
            dict: Dictionary with embeddings for job description sections
        """
        # For job descriptions, create embeddings for the entire text
        embeddings = {
            'full_description': self.get_embeddings(job_description_text)
        }

        # Extract and embed requirements section if available
        requirements_section = self._extract_requirements_section(job_description_text)
        if requirements_section:
            embeddings['requirements'] = self.get_embeddings(requirements_section)

        # Extract and embed responsibilities section if available
        responsibilities_section = self._extract_responsibilities_section(job_description_text)
        if responsibilities_section:
            embeddings['responsibilities'] = self.get_embeddings(responsibilities_section)

        return embeddings

    def _extract_requirements_section(self, text):
        """Extract requirements section from job description"""
        keywords = ["requirements", "qualifications", "what you'll need", "what we're looking for"]
        lines = text.split("\n")

        start_idx = -1
        end_idx = -1

        # Find the start of the requirements section
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in keywords):
                start_idx = i
                break

        if start_idx == -1:
            return ""

        # Find the end of the section (next heading)
        for i in range(start_idx + 1, len(lines)):
            if lines[i].strip() and lines[i][0].isupper() and lines[i].strip().endswith(":"):
                end_idx = i
                break

        if end_idx == -1:
            end_idx = len(lines)

        return "\n".join(lines[start_idx:end_idx])

    def _extract_responsibilities_section(self, text):
        """Extract responsibilities section from job description"""
        keywords = ["responsibilities", "duties", "what you'll do", "your role"]
        lines = text.split("\n")

        start_idx = -1
        end_idx = -1

        # Find the start of the responsibilities section
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in keywords):
                start_idx = i
                break

        if start_idx == -1:
            return ""

        # Find the end of the section (next heading)
        for i in range(start_idx + 1, len(lines)):
            if lines[i].strip() and lines[i][0].isupper() and lines[i].strip().endswith(":"):
                end_idx = i
                break

        if end_idx == -1:
            end_idx = len(lines)

        return "\n".join(lines[start_idx:end_idx])