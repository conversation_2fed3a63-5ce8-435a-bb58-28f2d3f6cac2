"""
Model Architectures for ResuMatch

This module provides different neural network architectures for the ResuMatch system.
These architectures can be used to experiment with different approaches to resume-job matching.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, Any, Optional

class SiameseNetwork(nn.Module):
    """
    Basic Siamese neural network for matching resume embeddings with job description embeddings.
    Maps both types of documents into a shared semantic space for comparison.
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, output_dim: int = 64, dropout: float = 0.2):
        """
        Initialize the Siamese Network
        
        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
            dropout (float): Dropout probability
        """
        super(SiameseNetwork, self).__init__()
        
        # Define network architecture
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.<PERSON>LU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim),
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the network
        
        Args:
            x (torch.Tensor): Input tensor
            
        Returns:
            torch.Tensor: Encoded representation
        """
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        return self.encoder(x)
    
    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for a pair of inputs
        
        Args:
            x1 (torch.Tensor): First input tensor
            x2 (torch.Tensor): Second input tensor
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Encoded representations of both inputs
        """
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

class DeepSiameseNetwork(nn.Module):
    """
    Deep Siamese neural network with multiple hidden layers for better representation learning.
    """
    
    def __init__(self, input_dim: int, hidden_dims: list = [256, 128], output_dim: int = 64, dropout: float = 0.2):
        """
        Initialize the Deep Siamese Network
        
        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dims (list): Dimensions of hidden layers
            output_dim (int): Dimension of output embeddings (shared space)
            dropout (float): Dropout probability
        """
        super(DeepSiameseNetwork, self).__init__()
        
        # Build encoder layers
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, output_dim))
        
        # Create encoder
        self.encoder = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        return self.encoder(x)
    
    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

class AttentionSiameseNetwork(nn.Module):
    """
    Siamese neural network with attention mechanism to focus on important features.
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, output_dim: int = 64, dropout: float = 0.2):
        """
        Initialize the Attention Siamese Network
        
        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
            dropout (float): Dropout probability
        """
        super(AttentionSiameseNetwork, self).__init__()
        
        # Feature transformation layers
        self.feature_layer = nn.Linear(input_dim, hidden_dim)
        self.attention = nn.Linear(hidden_dim, 1)
        
        # Encoder layers
        self.encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim),
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        
        # Transform features
        features = F.relu(self.feature_layer(x))
        
        # Apply attention
        attention_weights = F.softmax(self.attention(features), dim=1)
        weighted_features = features * attention_weights
        
        # Encode features
        output = self.encoder(weighted_features)
        
        return output
    
    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

class SiameseNetworkWithNormalization(nn.Module):
    """
    Siamese neural network with layer normalization for better training stability.
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, output_dim: int = 64, dropout: float = 0.2):
        """
        Initialize the Siamese Network with Normalization
        
        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
            dropout (float): Dropout probability
        """
        super(SiameseNetworkWithNormalization, self).__init__()
        
        # Define network architecture with layer normalization
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim),
            nn.LayerNorm(output_dim),
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        return self.encoder(x)
    
    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

class ResidualSiameseNetwork(nn.Module):
    """
    Siamese neural network with residual connections for better gradient flow.
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, output_dim: int = 64, dropout: float = 0.2):
        """
        Initialize the Residual Siamese Network
        
        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
            dropout (float): Dropout probability
        """
        super(ResidualSiameseNetwork, self).__init__()
        
        # Input projection if dimensions don't match
        self.input_projection = None
        if input_dim != hidden_dim:
            self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # Hidden layers
        self.hidden1 = nn.Linear(hidden_dim, hidden_dim)
        self.hidden2 = nn.Linear(hidden_dim, hidden_dim)
        
        # Normalization and dropout
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout)
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dim, output_dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)
        
        # Input projection if needed
        if self.input_projection is not None:
            x = self.input_projection(x)
        
        # First residual block
        residual = x
        x = self.norm1(x)
        x = F.relu(self.hidden1(x))
        x = self.dropout(x)
        x = x + residual
        
        # Second residual block
        residual = x
        x = self.norm2(x)
        x = F.relu(self.hidden2(x))
        x = self.dropout(x)
        x = x + residual
        
        # Output layer
        x = self.output_layer(x)
        
        return x
    
    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

# Dictionary mapping architecture names to model classes
ARCHITECTURES = {
    "basic": SiameseNetwork,
    "deep": DeepSiameseNetwork,
    "attention": AttentionSiameseNetwork,
    "normalized": SiameseNetworkWithNormalization,
    "residual": ResidualSiameseNetwork
}

def get_model(architecture: str, input_dim: int, **kwargs) -> nn.Module:
    """
    Get a model instance based on the specified architecture
    
    Args:
        architecture (str): Name of the architecture to use
        input_dim (int): Dimension of input embeddings
        **kwargs: Additional arguments to pass to the model constructor
        
    Returns:
        nn.Module: Model instance
    """
    if architecture not in ARCHITECTURES:
        raise ValueError(f"Unknown architecture: {architecture}. Available architectures: {list(ARCHITECTURES.keys())}")
    
    return ARCHITECTURES[architecture](input_dim, **kwargs)
