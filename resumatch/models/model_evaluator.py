"""
Model Evaluator for ResuMatch

This module provides functionality to evaluate and compare different model architectures
for the ResuMatch system.
"""

import os
import json
import time
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
from sklearn.metrics import precision_recall_curve, average_precision_score, roc_curve, auc
import logging

from resumatch.data.data_loader import DataLoader
from resumatch.embeddings.text_embedder import TextEmbedder
from resumatch.models.model_architectures import get_model, ARCHITECTURES
from resumatch.siamese_network.matcher import ContrastiveLoss

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/model_evaluation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("model-evaluator")

class ModelEvaluator:
    """
    Class for evaluating and comparing different model architectures.
    """
    
    def __init__(self, data_dir: str = None, models_dir: str = None):
        """
        Initialize the model evaluator
        
        Args:
            data_dir (str): Path to the data directory
            models_dir (str): Path to the models directory
        """
        # Set up directories
        base_path = Path(__file__).resolve().parent.parent.parent
        
        if data_dir is None:
            self.data_dir = os.path.join(base_path, "data")
        else:
            self.data_dir = data_dir
            
        if models_dir is None:
            self.models_dir = os.path.join(base_path, "models")
        else:
            self.models_dir = models_dir
        
        # Create directories if they don't exist
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(os.path.join(self.models_dir, "evaluation"), exist_ok=True)
        
        # Initialize components
        self.data_loader = DataLoader()
        self.text_embedder = TextEmbedder()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        logger.info(f"Model evaluator initialized. Using device: {self.device}")
    
    def evaluate_architectures(
        self, 
        resume_data_path: str, 
        job_data_path: str, 
        architectures: List[str] = None,
        epochs: int = 20,
        batch_size: int = 32,
        learning_rate: float = 0.001,
        test_split: float = 0.2,
        save_models: bool = True
    ) -> Dict[str, Dict[str, Any]]:
        """
        Evaluate multiple model architectures on the same dataset
        
        Args:
            resume_data_path (str): Path to the resume dataset
            job_data_path (str): Path to the job dataset
            architectures (List[str]): List of architecture names to evaluate
            epochs (int): Number of training epochs
            batch_size (int): Batch size for training
            learning_rate (float): Learning rate for optimization
            test_split (float): Fraction of data to use for testing
            save_models (bool): Whether to save the trained models
            
        Returns:
            Dict[str, Dict[str, Any]]: Evaluation results for each architecture
        """
        # Use all available architectures if none specified
        if architectures is None:
            architectures = list(ARCHITECTURES.keys())
        
        # Validate architectures
        for arch in architectures:
            if arch not in ARCHITECTURES:
                raise ValueError(f"Unknown architecture: {arch}. Available architectures: {list(ARCHITECTURES.keys())}")
        
        logger.info(f"Evaluating architectures: {architectures}")
        logger.info(f"Using resume data: {resume_data_path}")
        logger.info(f"Using job data: {job_data_path}")
        
        # Load and prepare data
        logger.info("Loading and preparing data...")
        resume_embeddings, job_embeddings = self._load_data(resume_data_path, job_data_path)
        
        # Generate training pairs
        resume_vectors, job_vectors, labels = self.data_loader.generate_training_pairs(
            resume_embeddings, job_embeddings
        )
        
        # Split into train and test sets
        train_data = self.data_loader.split_data(resume_vectors, job_vectors, labels, test_split=test_split)
        
        # Evaluate each architecture
        results = {}
        for arch in architectures:
            logger.info(f"Evaluating architecture: {arch}")
            arch_results = self._evaluate_architecture(
                arch, train_data, epochs, batch_size, learning_rate, save_models
            )
            results[arch] = arch_results
        
        # Save evaluation results
        results_path = os.path.join(self.models_dir, "evaluation", "architecture_comparison.json")
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Evaluation results saved to: {results_path}")
        
        # Generate comparison plots
        self._generate_comparison_plots(results)
        
        return results
    
    def _load_data(self, resume_data_path: str, job_data_path: str) -> Tuple[Dict[str, np.ndarray], Dict[str, np.ndarray]]:
        """
        Load and prepare data for evaluation
        
        Args:
            resume_data_path (str): Path to the resume dataset
            job_data_path (str): Path to the job dataset
            
        Returns:
            Tuple[Dict[str, np.ndarray], Dict[str, np.ndarray]]: Resume and job embeddings
        """
        # Load resume data
        with open(resume_data_path, 'r') as f:
            resume_data = json.load(f)
        
        # Load job data
        with open(job_data_path, 'r') as f:
            job_data = json.load(f)
        
        logger.info(f"Loaded {len(resume_data)} resumes and {len(job_data)} jobs")
        
        # Generate embeddings
        resume_embeddings = {}
        for resume in resume_data:
            resume_id = resume['resume_id']
            if 'text' in resume:
                resume_text = resume['text']
                resume_embeddings[resume_id] = self.text_embedder.get_embeddings(resume_text)[0]
            elif 'entities' in resume:
                entities = resume['entities']
                if 'skills' in entities:
                    skills_text = ", ".join(entities['skills'])
                    resume_embeddings[resume_id] = self.text_embedder.get_embeddings(skills_text)[0]
        
        job_embeddings = {}
        for job in job_data:
            job_id = job['job_id']
            if 'description' in job:
                job_text = job['description']
                if 'requirements' in job and job['requirements']:
                    job_text += f"\n\nRequirements: {job['requirements']}"
                if 'responsibilities' in job and job['responsibilities']:
                    job_text += f"\n\nResponsibilities: {job['responsibilities']}"
                job_embeddings[job_id] = self.text_embedder.get_embeddings(job_text)[0]
        
        logger.info(f"Generated embeddings for {len(resume_embeddings)} resumes and {len(job_embeddings)} jobs")
        
        return resume_embeddings, job_embeddings
    
    def _evaluate_architecture(
        self, 
        architecture: str, 
        train_data: Dict[str, np.ndarray], 
        epochs: int, 
        batch_size: int, 
        learning_rate: float,
        save_model: bool
    ) -> Dict[str, Any]:
        """
        Evaluate a single model architecture
        
        Args:
            architecture (str): Name of the architecture to evaluate
            train_data (Dict[str, np.ndarray]): Training and testing data
            epochs (int): Number of training epochs
            batch_size (int): Batch size for training
            learning_rate (float): Learning rate for optimization
            save_model (bool): Whether to save the trained model
            
        Returns:
            Dict[str, Any]: Evaluation results
        """
        start_time = time.time()
        
        # Get input dimension from data
        input_dim = train_data['X_train_resume'].shape[1]
        
        # Create model
        model = get_model(architecture, input_dim)
        model.to(self.device)
        
        # Set up optimizer and loss function
        optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        criterion = ContrastiveLoss(margin=1.0)
        
        # Training data
        X_train_resume = torch.tensor(train_data['X_train_resume'], dtype=torch.float32).to(self.device)
        X_train_job = torch.tensor(train_data['X_train_job'], dtype=torch.float32).to(self.device)
        y_train = torch.tensor(train_data['y_train'], dtype=torch.float32).to(self.device)
        
        # Testing data
        X_test_resume = torch.tensor(train_data['X_test_resume'], dtype=torch.float32).to(self.device)
        X_test_job = torch.tensor(train_data['X_test_job'], dtype=torch.float32).to(self.device)
        y_test = torch.tensor(train_data['y_test'], dtype=torch.float32).to(self.device)
        
        # Training loop
        logger.info(f"Training {architecture} model for {epochs} epochs...")
        train_losses = []
        test_losses = []
        test_metrics = []
        
        for epoch in range(epochs):
            # Training
            model.train()
            train_loss = 0.0
            
            # Process in batches
            num_batches = len(X_train_resume) // batch_size
            if num_batches == 0:
                num_batches = 1  # Handle small datasets
                
            for i in range(num_batches):
                start_idx = i * batch_size
                end_idx = min(start_idx + batch_size, len(X_train_resume))
                
                # Get batch
                resume_batch = X_train_resume[start_idx:end_idx]
                job_batch = X_train_job[start_idx:end_idx]
                labels_batch = y_train[start_idx:end_idx]
                
                # Forward pass
                resume_encoded, job_encoded = model.forward_pair(resume_batch, job_batch)
                
                # Calculate loss
                loss = criterion(resume_encoded, job_encoded, labels_batch)
                
                # Backward pass and optimize
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # Calculate average training loss
            avg_train_loss = train_loss / num_batches
            train_losses.append(avg_train_loss)
            
            # Evaluation
            model.eval()
            with torch.no_grad():
                # Forward pass
                resume_encoded, job_encoded = model.forward_pair(X_test_resume, X_test_job)
                
                # Calculate loss
                test_loss = criterion(resume_encoded, job_encoded, y_test).item()
                test_losses.append(test_loss)
                
                # Calculate metrics
                # Euclidean distance between encoded vectors
                distances = torch.sqrt(torch.sum((resume_encoded - job_encoded) ** 2, dim=1)).cpu().numpy()
                
                # Convert distances to similarity scores (1 - normalized distance)
                max_dist = np.max(distances)
                if max_dist > 0:
                    similarities = 1 - distances / max_dist
                else:
                    similarities = 1 - distances
                
                # Calculate metrics
                y_true = y_test.cpu().numpy()
                
                # Precision-Recall
                precision, recall, _ = precision_recall_curve(y_true, similarities)
                ap = average_precision_score(y_true, similarities)
                
                # ROC
                fpr, tpr, _ = roc_curve(y_true, similarities)
                roc_auc = auc(fpr, tpr)
                
                # Accuracy at threshold 0.5
                predictions = (similarities >= 0.5).astype(int)
                accuracy = np.mean(predictions == y_true)
                
                # F1 score
                true_positives = np.sum((predictions == 1) & (y_true == 1))
                false_positives = np.sum((predictions == 1) & (y_true == 0))
                false_negatives = np.sum((predictions == 0) & (y_true == 1))
                
                precision_score = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
                recall_score = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
                
                f1 = 2 * (precision_score * recall_score) / (precision_score + recall_score) if (precision_score + recall_score) > 0 else 0
                
                test_metrics.append({
                    'epoch': epoch + 1,
                    'loss': test_loss,
                    'accuracy': accuracy,
                    'precision': precision_score,
                    'recall': recall_score,
                    'f1': f1,
                    'ap': ap,
                    'roc_auc': roc_auc
                })
            
            # Log progress
            if (epoch + 1) % 5 == 0 or epoch == 0 or epoch == epochs - 1:
                logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {avg_train_loss:.4f}, Test Loss: {test_loss:.4f}, "
                           f"Accuracy: {accuracy:.4f}, F1: {f1:.4f}, ROC AUC: {roc_auc:.4f}")
        
        # Save model if requested
        if save_model:
            model_path = os.path.join(self.models_dir, f"siamese_model_{architecture}.pth")
            torch.save(model.state_dict(), model_path)
            logger.info(f"Model saved to: {model_path}")
        
        # Calculate training time
        training_time = time.time() - start_time
        
        # Get final metrics
        final_metrics = test_metrics[-1]
        
        # Prepare results
        results = {
            'architecture': architecture,
            'input_dim': input_dim,
            'epochs': epochs,
            'batch_size': batch_size,
            'learning_rate': learning_rate,
            'training_time': training_time,
            'final_metrics': final_metrics,
            'train_losses': train_losses,
            'test_losses': test_losses,
            'test_metrics': test_metrics
        }
        
        return results
    
    def _generate_comparison_plots(self, results: Dict[str, Dict[str, Any]]):
        """
        Generate comparison plots for different architectures
        
        Args:
            results (Dict[str, Dict[str, Any]]): Evaluation results for each architecture
        """
        # Create plots directory
        plots_dir = os.path.join(self.models_dir, "evaluation", "plots")
        os.makedirs(plots_dir, exist_ok=True)
        
        # Plot training and test losses
        plt.figure(figsize=(12, 6))
        for arch, arch_results in results.items():
            epochs = range(1, len(arch_results['train_losses']) + 1)
            plt.plot(epochs, arch_results['train_losses'], 'o-', label=f"{arch} (train)")
            plt.plot(epochs, arch_results['test_losses'], 's--', label=f"{arch} (test)")
        
        plt.title('Training and Test Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(plots_dir, 'loss_comparison.png'))
        
        # Plot metrics comparison
        metrics = ['accuracy', 'precision', 'recall', 'f1', 'ap', 'roc_auc']
        
        for metric in metrics:
            plt.figure(figsize=(12, 6))
            for arch, arch_results in results.items():
                values = [m[metric] for m in arch_results['test_metrics']]
                epochs = range(1, len(values) + 1)
                plt.plot(epochs, values, 'o-', label=arch)
            
            plt.title(f'{metric.upper()} Comparison')
            plt.xlabel('Epoch')
            plt.ylabel(metric.upper())
            plt.legend()
            plt.grid(True)
            plt.savefig(os.path.join(plots_dir, f'{metric}_comparison.png'))
        
        # Bar chart of final metrics
        plt.figure(figsize=(14, 8))
        architectures = list(results.keys())
        x = np.arange(len(architectures))
        width = 0.15
        
        for i, metric in enumerate(metrics):
            values = [results[arch]['final_metrics'][metric] for arch in architectures]
            plt.bar(x + i*width, values, width, label=metric.upper())
        
        plt.xlabel('Architecture')
        plt.ylabel('Score')
        plt.title('Final Metrics Comparison')
        plt.xticks(x + width*2, architectures)
        plt.legend()
        plt.grid(True, axis='y')
        plt.savefig(os.path.join(plots_dir, 'final_metrics_comparison.png'))
        
        # Training time comparison
        plt.figure(figsize=(10, 6))
        times = [results[arch]['training_time'] for arch in architectures]
        plt.bar(architectures, times)
        plt.title('Training Time Comparison')
        plt.xlabel('Architecture')
        plt.ylabel('Time (seconds)')
        plt.grid(True, axis='y')
        plt.savefig(os.path.join(plots_dir, 'training_time_comparison.png'))
        
        logger.info(f"Comparison plots saved to: {plots_dir}")

def evaluate_models():
    """
    Evaluate different model architectures
    """
    # Set up paths
    base_path = Path(__file__).resolve().parent.parent.parent
    data_dir = os.path.join(base_path, "data")
    
    # Find resume and job data files
    resume_data_path = os.path.join(data_dir, "enhanced", "enhanced_resumes.json")
    if not os.path.exists(resume_data_path):
        resume_data_path = os.path.join(data_dir, "processed_resumes.json")
        if not os.path.exists(resume_data_path):
            resume_data_path = os.path.join(data_dir, "datasets", "processed_resumes.json")
    
    job_data_path = os.path.join(data_dir, "enhanced", "enhanced_jobs.json")
    if not os.path.exists(job_data_path):
        job_data_path = os.path.join(data_dir, "generated_jobs.json")
        if not os.path.exists(job_data_path):
            job_data_path = os.path.join(data_dir, "datasets", "generated_jobs.json")
    
    # Check if data files exist
    if not os.path.exists(resume_data_path):
        print(f"Resume data file not found at {resume_data_path}")
        return
    
    if not os.path.exists(job_data_path):
        print(f"Job data file not found at {job_data_path}")
        return
    
    print(f"Using resume data: {resume_data_path}")
    print(f"Using job data: {job_data_path}")
    
    # Create evaluator
    evaluator = ModelEvaluator()
    
    # Evaluate architectures
    architectures = ["basic", "deep", "normalized", "residual"]  # Skip attention for faster evaluation
    results = evaluator.evaluate_architectures(
        resume_data_path=resume_data_path,
        job_data_path=job_data_path,
        architectures=architectures,
        epochs=10,  # Reduced for faster evaluation
        batch_size=32,
        learning_rate=0.001,
        test_split=0.2,
        save_models=True
    )
    
    # Print summary of results
    print("\nEvaluation Results Summary:")
    print("===========================")
    for arch, arch_results in results.items():
        metrics = arch_results['final_metrics']
        print(f"\n{arch.upper()} Architecture:")
        print(f"  Accuracy: {metrics['accuracy']:.4f}")
        print(f"  F1 Score: {metrics['f1']:.4f}")
        print(f"  ROC AUC: {metrics['roc_auc']:.4f}")
        print(f"  Training Time: {arch_results['training_time']:.2f} seconds")
    
    print("\nDetailed results and plots saved to models/evaluation directory")

if __name__ == "__main__":
    evaluate_models()
