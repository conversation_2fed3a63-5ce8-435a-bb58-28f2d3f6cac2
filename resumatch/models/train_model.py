import os
import sys
import json
import numpy as np
import argparse
from pathlib import Path

# Add parent directory to path
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from resumatch.embeddings.text_embedder import TextEmbedder
from resumatch.siamese_network.matcher import ResumeMatcher
from resumatch.data.data_loader import DataLoader
from resumatch.utils.helpers import setup_directories, log_event

def train_model(
    resume_data_path,
    job_data_path,
    embedding_dim=384,
    epochs=20,
    batch_size=32,
    output_path=None,
    test_split=0.2
):
    """
    Train the Siamese network model for resume-job matching

    Args:
        resume_data_path (str): Path to resume data JSON file
        job_data_path (str): Path to job data JSON file
        embedding_dim (int): Dimension of embeddings
        epochs (int): Number of training epochs
        batch_size (int): Batch size for training
        output_path (str): Path to save the trained model
        test_split (float): Proportion of data to use for testing
    """
    # Set up directories
    base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    directories = setup_directories(base_path)

    if output_path is None:
        output_path = os.path.join(directories['models'], 'siamese_model.pth')

    # Set up logging
    log_file = os.path.join(directories['logs'], 'training.log')
    log_event(log_file, "INFO", f"Starting model training with data from {resume_data_path} and {job_data_path}")

    # Load data
    data_loader = DataLoader()
    resume_data = data_loader.load_resume_data(resume_data_path)
    job_data = data_loader.load_job_data(job_data_path)

    if not resume_data or not job_data:
        log_event(log_file, "ERROR", "Failed to load data or empty data files")
        print("Error: Failed to load data or empty data files")
        return

    log_event(log_file, "INFO", f"Loaded {len(resume_data)} resume records and {len(job_data)} job records")
    print(f"Loaded {len(resume_data)} resume records and {len(job_data)} job records")

    # Initialize embedder
    text_embedder = TextEmbedder()

    # Generate embeddings for resumes
    resume_embeddings = {}
    print("Generating resume embeddings...")
    for resume in resume_data:
        try:
            if 'entities' in resume:
                resume_embedding = text_embedder.get_resume_embeddings(resume['entities'])
                if 'full_resume' in resume_embedding:
                    resume_embeddings[resume['resume_id']] = resume_embedding['full_resume']
        except Exception as e:
            log_event(log_file, "ERROR", f"Error generating embedding for resume {resume.get('resume_id', 'unknown')}: {str(e)}")

    # Generate embeddings for jobs
    job_embeddings = {}
    print("Generating job description embeddings...")
    for job in job_data:
        try:
            if 'description' in job:
                full_description = job['description']
                if 'requirements' in job:
                    full_description += f"\n\nRequirements:\n{job['requirements']}"
                if 'responsibilities' in job:
                    full_description += f"\n\nResponsibilities:\n{job['responsibilities']}"

                job_embedding = text_embedder.get_job_description_embeddings(full_description)
                if 'full_description' in job_embedding:
                    job_embeddings[job['job_id']] = job_embedding['full_description']
        except Exception as e:
            log_event(log_file, "ERROR", f"Error generating embedding for job {job.get('job_id', 'unknown')}: {str(e)}")

    # Generate training and test data
    print("Preparing training and test data...")
    resume_vectors, job_vectors, labels = data_loader.generate_training_pairs(resume_embeddings, job_embeddings)

    # Split into train and test sets
    train_data = data_loader.split_data(resume_vectors, job_vectors, labels, test_size=test_split)
    train_resume, train_job, train_labels, test_resume, test_job, test_labels = train_data

    # Initialize and train the matcher model
    print(f"Training model with {len(train_labels)} samples...")
    resume_matcher = ResumeMatcher(embedding_dim)
    losses = resume_matcher.train(train_resume, train_job, train_labels, num_epochs=epochs, batch_size=batch_size)

    # Evaluate on test set
    print("Evaluating model...")
    metrics = resume_matcher.evaluate(test_resume, test_job, test_labels)

    # Save model
    resume_matcher.save_model(output_path)
    log_event(log_file, "INFO", f"Model saved to {output_path}")

    # Log and print results
    log_event(log_file, "INFO", f"Training complete. Test metrics: {metrics}")
    print("\nTraining complete!")
    print(f"Model saved to {output_path}")
    print(f"Test metrics: Accuracy={metrics['accuracy']:.4f}, Precision={metrics['precision']:.4f}, Recall={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train the ResuMatch Siamese network model')
    parser.add_argument('--resume-data', type=str, required=True, help='Path to resume data JSON file')
    parser.add_argument('--job-data', type=str, required=True, help='Path to job data JSON file')
    parser.add_argument('--embedding-dim', type=int, default=384, help='Dimension of embeddings')
    parser.add_argument('--epochs', type=int, default=20, help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size for training')
    parser.add_argument('--output', type=str, default=None, help='Path to save the trained model')
    parser.add_argument('--test-split', type=float, default=0.2, help='Proportion of data to use for testing')

    args = parser.parse_args()

    # Train model
    train_model(
        args.resume_data,
        args.job_data,
        args.embedding_dim,
        args.epochs,
        args.batch_size,
        args.output,
        args.test_split
    )