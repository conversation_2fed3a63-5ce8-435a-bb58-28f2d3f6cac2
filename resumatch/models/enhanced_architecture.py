"""
Enhanced Model Architectures for ResuMatch

This module provides advanced neural network architectures for the ResuMatch system
designed to achieve higher accuracy in resume-job matching.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, Any, Optional, List

class EnhancedSiameseNetwork(nn.Module):
    """
    Enhanced Siamese neural network with attention mechanism and deeper architecture.
    """

    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 256, 128],
                 output_dim: int = 64, dropout: float = 0.4, use_batch_norm: bool = False):
        """
        Initialize the Enhanced Siamese Network

        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dims (list): Dimensions of hidden layers
            output_dim (int): Dimension of output embeddings (shared space)
            dropout (float): Dropout probability
            use_batch_norm (bool): Whether to use batch normalization (not recommended)
        """
        super(EnhancedSiameseNetwork, self).__init__()

        # Feature extraction layers
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.LayerNorm(hidden_dims[0]),  # Layer normalization instead of batch norm
            nn.GELU(),  # GELU activation (better than ReLU/LeakyReLU)
            nn.Dropout(dropout),

            nn.Linear(hidden_dims[0], hidden_dims[1]),
            nn.LayerNorm(hidden_dims[1]),
            nn.GELU(),
            nn.Dropout(dropout),

            nn.Linear(hidden_dims[1], hidden_dims[2]),
            nn.LayerNorm(hidden_dims[2]),
            nn.GELU(),
            nn.Dropout(dropout/2)
        )

        # Multi-head self-attention
        self.num_heads = 4
        self.head_dim = hidden_dims[2] // self.num_heads

        # Query, Key, Value projections
        self.query = nn.Linear(hidden_dims[2], hidden_dims[2])
        self.key = nn.Linear(hidden_dims[2], hidden_dims[2])
        self.value = nn.Linear(hidden_dims[2], hidden_dims[2])

        # Output projection
        self.attn_out = nn.Linear(hidden_dims[2], hidden_dims[2])

        # Layer norm and residual
        self.layer_norm1 = nn.LayerNorm(hidden_dims[2])
        self.layer_norm2 = nn.LayerNorm(hidden_dims[2])

        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dims[2], hidden_dims[2] * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[2] * 4, hidden_dims[2]),
            nn.Dropout(dropout)
        )

        # Output layer
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dims[2], output_dim),
            nn.LayerNorm(output_dim),
            nn.Tanh()  # Tanh activation for better similarity calculation
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network with transformer-like architecture"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)

        # Feature extraction
        x = self.feature_extractor(x)

        # Multi-head self-attention with residual connection and layer norm
        residual = x

        # Split heads
        batch_size = x.size(0)

        # Self-attention
        q = self.query(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.key(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.value(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # Scaled dot-product attention
        scores = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn_weights = F.softmax(scores, dim=-1)
        attn_output = torch.matmul(attn_weights, v)

        # Reshape back
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, -1, self.num_heads * self.head_dim)
        attn_output = self.attn_out(attn_output)

        # Residual connection and layer norm
        x = self.layer_norm1(residual + attn_output)

        # Feed-forward network with residual connection
        residual = x
        x = self.ffn(x)
        x = self.layer_norm2(residual + x)

        # Output layer
        x = self.output_layer(x.squeeze(1))

        # L2 normalize output embeddings
        x = F.normalize(x, p=2, dim=1)

        return x

    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

class EnsembleModel(nn.Module):
    """
    Advanced ensemble model that combines multiple architectures with attention-based weighting
    for better performance and higher accuracy.
    """

    def __init__(self, input_dim: int, models_config: List[Dict[str, Any]]):
        """
        Initialize the Advanced Ensemble Model

        Args:
            input_dim (int): Dimension of input embeddings
            models_config (List[Dict]): Configuration for each model in the ensemble
        """
        super(EnsembleModel, self).__init__()

        # Create models
        self.models = nn.ModuleList()
        self.model_output_dims = []

        for config in models_config:
            model_type = config.pop('type')
            output_dim = config.get('output_dim', 128)
            self.model_output_dims.append(output_dim)

            if model_type == 'enhanced':
                self.models.append(EnhancedSiameseNetwork(input_dim, **config))
            elif model_type == 'residual':
                from resumatch.models.model_architectures import ResidualSiameseNetwork
                self.models.append(ResidualSiameseNetwork(input_dim, **config))
            elif model_type == 'normalized':
                from resumatch.models.model_architectures import SiameseNetworkWithNormalization
                self.models.append(SiameseNetworkWithNormalization(input_dim, **config))

        # Dynamic attention mechanism for model weighting
        self.attention = nn.Sequential(
            nn.Linear(len(self.models), 64),  # Input is the number of models
            nn.LayerNorm(64),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(64, len(self.models)),
        )

        # Output dimension is the maximum of all model output dimensions
        self.output_dim = max(self.model_output_dims)

        # Projection layers to align dimensions
        self.projections = nn.ModuleList()
        for dim in self.model_output_dims:
            if dim != self.output_dim:
                self.projections.append(nn.Linear(dim, self.output_dim))
            else:
                self.projections.append(nn.Identity())

        # Final layer for combining features
        self.final_layer = nn.Sequential(
            nn.Linear(self.output_dim, self.output_dim),
            nn.LayerNorm(self.output_dim),
            nn.Tanh()
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the ensemble with dynamic attention weighting"""
        # Get outputs from all models
        model_outputs = []
        for i, model in enumerate(self.models):
            output = model(x)

            # Ensure output is 2D (batch_size, features)
            if output.dim() > 2:
                output = output.squeeze(1)

            # Project to common dimension if needed
            output = self.projections[i](output)
            model_outputs.append(output)

        # Stack outputs for attention (instead of concatenating)
        # Shape: [batch_size, num_models, output_dim]
        stacked_outputs = torch.stack(model_outputs, dim=1)

        # Calculate attention weights - use average of each model's output
        avg_outputs = torch.mean(stacked_outputs, dim=2)  # [batch_size, num_models]
        attention_logits = self.attention(avg_outputs)
        attention_weights = F.softmax(attention_logits, dim=1)  # [batch_size, num_models]

        # Apply attention weights to each model output
        # Reshape attention weights to [batch_size, num_models, 1]
        attention_weights = attention_weights.unsqueeze(2)

        # Weighted sum of model outputs
        # [batch_size, num_models, output_dim] * [batch_size, num_models, 1]
        weighted_outputs = stacked_outputs * attention_weights

        # Sum across models dimension
        ensemble_output = torch.sum(weighted_outputs, dim=1)  # [batch_size, output_dim]

        # Final processing
        ensemble_output = self.final_layer(ensemble_output)

        # Normalize output
        ensemble_output = F.normalize(ensemble_output, p=2, dim=1)

        return ensemble_output

    def forward_pair(self, x1: torch.Tensor, x2: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2

class AdvancedLoss(nn.Module):
    """
    Advanced loss function that combines contrastive loss with triplet loss
    and uses hard negative mining for better performance.
    """

    def __init__(self, margin: float = 1.0, lambda_triplet: float = 0.5):
        """
        Initialize the advanced loss function

        Args:
            margin (float): Margin for contrastive and triplet loss
            lambda_triplet (float): Weight for triplet loss component
        """
        super(AdvancedLoss, self).__init__()
        self.margin = margin
        self.lambda_triplet = lambda_triplet

    def forward(self, anchor: torch.Tensor, positive: torch.Tensor,
                negative: Optional[torch.Tensor] = None,
                labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Calculate the advanced loss

        Args:
            anchor: Anchor embeddings
            positive: Positive embeddings (same class as anchor)
            negative: Negative embeddings (different class from anchor)
            labels: Binary labels (1 for similar pair, 0 for dissimilar pair)

        Returns:
            torch.Tensor: Loss value
        """
        # If labels are provided, use contrastive loss
        if labels is not None:
            # Calculate euclidean distance
            euclidean_distance = torch.nn.functional.pairwise_distance(anchor, positive)

            # Contrastive loss calculation
            contrastive_loss = torch.mean((1 - labels) * torch.pow(euclidean_distance, 2) +  # Similar pairs: minimize distance
                                (labels) * torch.pow(torch.clamp(self.margin - euclidean_distance, min=0.0), 2))  # Dissimilar pairs: push apart

            return contrastive_loss

        # If negative samples are provided, use triplet loss
        elif negative is not None:
            # Calculate distances
            pos_dist = torch.nn.functional.pairwise_distance(anchor, positive)
            neg_dist = torch.nn.functional.pairwise_distance(anchor, negative)

            # Triplet loss calculation
            triplet_loss = torch.mean(torch.clamp(pos_dist - neg_dist + self.margin, min=0.0))

            return triplet_loss

        # If both are provided, use combined loss
        elif labels is not None and negative is not None:
            # Calculate contrastive loss
            euclidean_distance = torch.nn.functional.pairwise_distance(anchor, positive)
            contrastive_loss = torch.mean((1 - labels) * torch.pow(euclidean_distance, 2) +
                                (labels) * torch.pow(torch.clamp(self.margin - euclidean_distance, min=0.0), 2))

            # Calculate triplet loss
            pos_dist = torch.nn.functional.pairwise_distance(anchor, positive)
            neg_dist = torch.nn.functional.pairwise_distance(anchor, negative)
            triplet_loss = torch.mean(torch.clamp(pos_dist - neg_dist + self.margin, min=0.0))

            # Combined loss
            combined_loss = contrastive_loss + self.lambda_triplet * triplet_loss

            return combined_loss

        else:
            raise ValueError("Either labels or negative samples must be provided")

# Dictionary mapping architecture names to model classes
ENHANCED_ARCHITECTURES = {
    "enhanced": EnhancedSiameseNetwork,
    "ensemble": EnsembleModel
}

def get_enhanced_model(architecture: str, input_dim: int, **kwargs) -> nn.Module:
    """
    Get an enhanced model instance based on the specified architecture

    Args:
        architecture (str): Name of the architecture to use
        input_dim (int): Dimension of input embeddings
        **kwargs: Additional arguments to pass to the model constructor

    Returns:
        nn.Module: Model instance
    """
    if architecture not in ENHANCED_ARCHITECTURES:
        raise ValueError(f"Unknown architecture: {architecture}. Available architectures: {list(ENHANCED_ARCHITECTURES.keys())}")

    return ENHANCED_ARCHITECTURES[architecture](input_dim, **kwargs)
