#!/usr/bin/env python3
"""
Test script for enhanced entity extraction capabilities
"""

import requests
import json
import time

def test_enhanced_resume_extraction():
    """Test the enhanced resume extraction with structured data"""
    base_url = "http://localhost:8000"
    
    print("🔄 Testing enhanced resume extraction...")
    
    # Upload the resume
    try:
        with open("test_resume.pdf", "rb") as f:
            files = {"file": ("test_resume.pdf", f, "application/pdf")}
            data = {"name": "<PERSON><PERSON><PERSON><PERSON> Singh Resume - Enhanced Test"}
            
            response = requests.post(f"{base_url}/api/documents/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                resume_id = result["resume_id"]
                print(f"✅ Resume uploaded successfully! Resume ID: {resume_id}")
                
                # Wait for processing
                time.sleep(2)
                
                # Get detailed resume information
                response = requests.get(f"{base_url}/api/resumes/{resume_id}")
                
                if response.status_code == 200:
                    resume_data = response.json()
                    print(f"\n📄 Resume Details:")
                    print(f"   Name: {resume_data.get('name', 'N/A')}")
                    print(f"   Uploaded at: {resume_data.get('uploaded_at', 'N/A')}")
                    
                    # Check if entities were extracted
                    entities = resume_data.get('entities', {})
                    if entities:
                        print(f"\n🧠 Extracted Entities:")
                        
                        # Display personal information
                        print(f"\n👤 Personal Information:")
                        print(f"   Name: {entities.get('name', 'Not found')}")
                        print(f"   Email: {entities.get('email', 'Not found')}")
                        print(f"   Phone: {entities.get('phone', 'Not found')}")
                        print(f"   Summary: {entities.get('summary', 'Not found')}")
                        
                        # Display categorized skills
                        skills = entities.get('skills', {})
                        if skills and isinstance(skills, dict):
                            print(f"\n🛠️ Skills by Category:")
                            for category, skill_list in skills.items():
                                if skill_list:
                                    print(f"   {category}:")
                                    for skill in skill_list[:5]:  # Show first 5 skills
                                        print(f"     • {skill}")
                                    if len(skill_list) > 5:
                                        print(f"     ... and {len(skill_list) - 5} more")
                        
                        # Display work experience
                        work_experience = entities.get('work_experience', [])
                        if work_experience:
                            print(f"\n💼 Work Experience:")
                            for i, exp in enumerate(work_experience, 1):
                                print(f"   {i}. {exp.get('title', 'N/A')} at {exp.get('organization', 'N/A')}")
                                print(f"      Dates: {exp.get('dates', 'N/A')}")
                                if exp.get('description'):
                                    print(f"      Description: {exp.get('description')[:100]}...")
                                print()
                        
                        # Display projects
                        projects = entities.get('projects', [])
                        if projects:
                            print(f"\n🚀 Projects:")
                            for i, project in enumerate(projects, 1):
                                print(f"   {i}. {project.get('title', 'N/A')}")
                                print(f"      Technologies: {project.get('technologies', 'N/A')}")
                                if project.get('description'):
                                    print(f"      Description: {project.get('description')[:100]}...")
                                print()
                        
                        # Display education
                        education = entities.get('education', [])
                        if education:
                            print(f"\n🎓 Education:")
                            for i, edu in enumerate(education, 1):
                                print(f"   {i}. {edu.get('degree', 'N/A')}")
                                print(f"      Institution: {edu.get('institution', 'N/A')}")
                                print(f"      Year: {edu.get('year', 'N/A')}")
                                print()
                        
                        # Display URLs
                        urls = entities.get('urls', [])
                        if urls:
                            print(f"\n🔗 URLs:")
                            for url in urls:
                                print(f"   • {url}")
                    
                    else:
                        print("❌ No entities found in resume data")
                    
                    return resume_id
                else:
                    print(f"❌ Failed to get resume details - {response.status_code}")
                    return resume_id
            else:
                print(f"❌ Failed to upload resume - {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
    except FileNotFoundError:
        print("❌ Resume file not found. Make sure 'test_resume.pdf' exists.")
        return None
    except Exception as e:
        print(f"❌ Error testing enhanced extraction: {e}")
        return None

def test_matching_with_enhanced_data(resume_id):
    """Test job matching with the enhanced resume data"""
    base_url = "http://localhost:8000"
    
    print(f"\n🎯 Testing job matching with enhanced data...")
    
    try:
        # Test matching with different thresholds
        thresholds = [0.5, 0.6, 0.7]
        
        for threshold in thresholds:
            print(f"\n--- Matching with threshold: {threshold} ---")
            
            response = requests.get(
                f"{base_url}/api/documents/match",
                params={
                    "resume_id": resume_id,
                    "threshold": threshold,
                    "limit": 5
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                matches = result.get("matches", [])
                
                print(f"✅ Found {len(matches)} matches")
                
                for i, match in enumerate(matches, 1):
                    score = match['match_score']
                    print(f"   {i}. {match['title']} at {match['company']}")
                    print(f"      Match Score: {score:.2f} ({score*100:.1f}%)")
                    
                    # Categorize the match quality
                    if score >= 0.8:
                        quality = "🟢 Excellent"
                    elif score >= 0.7:
                        quality = "🟡 Good"
                    elif score >= 0.6:
                        quality = "🟠 Fair"
                    else:
                        quality = "🔴 Poor"
                    print(f"      Quality: {quality}")
                    print()
                    
            else:
                print(f"❌ Failed to get matches - {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error testing matching: {e}")

def main():
    """Main test function"""
    print("=" * 70)
    print(" 🧪 Enhanced Entity Extraction Test")
    print("=" * 70)
    
    # Test enhanced resume extraction
    resume_id = test_enhanced_resume_extraction()
    
    if resume_id:
        # Test matching with enhanced data
        test_matching_with_enhanced_data(resume_id)
        
        print("\n" + "=" * 70)
        print(" ✅ Enhanced extraction testing completed!")
        print("=" * 70)
        print("\n📋 Summary of enhancements:")
        print("   • ✅ Categorized skills extraction")
        print("   • ✅ Enhanced work experience parsing")
        print("   • ✅ Improved project detection")
        print("   • ✅ Structured entity response format")
        print("   • ✅ Better resume-to-job matching")
        print("\n🌐 View detailed API docs at: http://127.0.0.1:8000/docs")
    else:
        print("\n❌ Enhanced extraction test failed")

if __name__ == "__main__":
    main()
