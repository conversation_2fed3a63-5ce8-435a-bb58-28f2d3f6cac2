<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResuMatch - Resume to Job Matching</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f1f8ff;
            border-bottom: 1px solid #e3f2fd;
            border-radius: 10px 10px 0 0 !important;
        }
        .shadow {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12 text-center mb-4">
                <h1 class="display-4">ResuMatch</h1>
                <p class="lead">AI-powered Resume to Job Matching</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-body">
                        <h2 class="text-center mb-4">Static Demo Page</h2>
                        <p class="text-center">This is a static demo page for the ResuMatch application.</p>
                        <p class="text-center">The full application requires FastAPI to be running properly.</p>
                        <div class="text-center mt-4">
                            <button class="btn btn-primary" disabled>Match Resume to Job</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-body">
                        <h3>How It Works</h3>
                        <p>ResuMatch uses advanced AI to analyze your resume and the job description to determine how well they match. The system:</p>
                        <ol>
                            <li>Extracts text and key information from your resume</li>
                            <li>Analyzes the job description for requirements and responsibilities</li>
                            <li>Uses a state-of-the-art neural network to compare the documents</li>
                            <li>Provides a match score and highlights key matching points</li>
                        </ol>
                        <p>This helps you understand how well your resume aligns with the job requirements and where you might need to make adjustments.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="mt-5 py-3 bg-light">
        <div class="container text-center">
            <p class="text-muted">ResuMatch - AI-powered Resume to Job Matching</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
