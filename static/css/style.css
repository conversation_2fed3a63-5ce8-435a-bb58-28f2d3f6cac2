/* Modern ResuMatch UI - Contemporary Design System */

/* CSS Custom Properties for Design System */
:root {
  /* Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;
  
  --accent-50: #fdf4ff;
  --accent-100: #fae8ff;
  --accent-200: #f5d0fe;
  --accent-300: #f0abfc;
  --accent-400: #e879f9;
  --accent-500: #d946ef;
  --accent-600: #c026d3;
  --accent-700: #a21caf;
  --accent-800: #86198f;
  --accent-900: #701a75;
  
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Light Mode Variables */
  --bg-primary: #ffffff;
  --bg-secondary: var(--neutral-50);
  --bg-tertiary: var(--neutral-100);
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-500);
  --border-primary: var(--neutral-200);
  --border-secondary: var(--neutral-300);
}

/* Dark Mode Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--neutral-900);
    --bg-secondary: var(--neutral-800);
    --bg-tertiary: var(--neutral-700);
    --text-primary: var(--neutral-50);
    --text-secondary: var(--neutral-300);
    --text-tertiary: var(--neutral-400);
    --border-primary: var(--neutral-700);
    --border-secondary: var(--neutral-600);
  }
}

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--accent-50) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography Hierarchy */
.display-4 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--primary-600), var(--accent-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
}

.lead {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

h2 {
  font-size: 2rem;
  font-weight: 700;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

h4 {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--space-3));
}

.col-md-6 {
  flex: 0 0 50%;
  padding: 0 var(--space-3);
}

.col-md-12 {
  flex: 0 0 100%;
  padding: 0 var(--space-3);
}

@media (max-width: 768px) {
  .col-md-6 {
    flex: 0 0 100%;
  }
  
  .container {
    padding: 0 var(--space-4);
  }
}

/* Modern Card Design */
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-8);
  overflow: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.card-body {
  padding: var(--space-8);
}

/* Modern Form Elements */
.form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  display: block;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-control, .form-select {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: var(--font-sans);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  margin-bottom: var(--space-5);
}

.form-control:focus, .form-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
  transform: translateY(-1px);
}

.form-control::placeholder {
  color: var(--text-tertiary);
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
  font-family: var(--font-sans);
  line-height: 1.6;
}

/* File Upload Styling */
input[type="file"] {
  position: relative;
  background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
  border: 2px dashed var(--primary-300);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

input[type="file"]:hover {
  border-color: var(--primary-500);
  background: linear-gradient(135deg, var(--primary-100), var(--accent-100));
}

/* Modern Button Design */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
}

.btn-lg {
  padding: var(--space-5) var(--space-8);
  font-size: 1.125rem;
}

/* Progress Bars */
.progress {
  height: 24px;
  background: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgb(0 0 0 / 0.1);
  margin-bottom: var(--space-4);
}

.progress-bar {
  height: 100%;
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-shadow: 0 1px 2px rgb(0 0 0 / 0.3);
  transition: width var(--transition-slow);
}

.progress-bar.bg-success {
  background: linear-gradient(90deg, var(--success-500), var(--success-600));
}

.progress-bar.bg-warning {
  background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.progress-bar.bg-danger {
  background: linear-gradient(90deg, var(--error-500), var(--error-600));
}

/* Match Score Container */
.match-score-container {
  padding: var(--space-8);
  text-align: center;
  background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
  border-radius: var(--radius-xl);
  margin: var(--space-6) 0;
}

.match-score-container .progress {
  height: 32px;
  margin: var(--space-4) 0;
}

/* Section Scores */
.section-scores {
  margin-top: var(--space-8);
}

.section-scores .progress {
  height: 20px;
  margin-bottom: var(--space-3);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge.bg-success {
  background: var(--success-500);
  color: white;
}

.me-2 {
  margin-right: var(--space-2);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

/* List Groups */
.list-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.list-group-item {
  padding: var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-500);
  transition: all var(--transition-fast);
}

.list-group-item:hover {
  background: var(--primary-50);
  transform: translateX(4px);
  border-left-color: var(--accent-500);
}

/* Job Description */
.job-description {
  max-height: 400px;
  overflow-y: auto;
  padding: var(--space-6);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  line-height: 1.7;
}

.job-description::-webkit-scrollbar {
  width: 6px;
}

.job-description::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-sm);
}

.job-description::-webkit-scrollbar-thumb {
  background: var(--primary-300);
  border-radius: var(--radius-sm);
}

.job-description::-webkit-scrollbar-thumb:hover {
  background: var(--primary-500);
}

/* Alerts */
.alert {
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--space-6);
}

.alert-warning {
  background: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.alert-danger {
  background: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

/* Footer */
footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-8) 0;
  margin-top: var(--space-16);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-secondary);
}

.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }

.d-none {
  display: none;
}

.d-flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }
  
  .card-body {
    padding: var(--space-6);
  }
  
  .display-4 {
    font-size: 2.5rem;
  }
  
  .btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .card-body {
    padding: var(--space-4);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Additional Modern UI Enhancements */

/* Hero Section Styling */
.hero-section {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--accent-50) 50%, var(--neutral-50) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
}

.hero-content {
  position: relative;
  z-index: 1;
}

/* Process Steps Styling */
.process-step {
  padding: var(--space-6);
  transition: all var(--transition-normal);
}

.process-step:hover {
  transform: translateY(-8px);
}

.step-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-500), var(--accent-500));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.step-icon i {
  font-size: 2rem;
  color: white;
}

.process-step:hover .step-icon {
  transform: scale(1.1);
  box-shadow: var(--shadow-2xl);
}

/* File Upload Enhancements */
.file-upload-wrapper {
  position: relative;
  border: 2px dashed var(--primary-300);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-align: center;
  background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
  transition: all var(--transition-normal);
  cursor: pointer;
}

.file-upload-wrapper:hover {
  border-color: var(--primary-500);
  background: linear-gradient(135deg, var(--primary-100), var(--accent-100));
  transform: translateY(-2px);
}

.file-upload-hint {
  pointer-events: none;
}

.file-info {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background: var(--success-50);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  text-align: center;
  max-width: 400px;
  box-shadow: var(--shadow-2xl);
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.card {
  animation: fadeInUp 0.6s ease-out;
}

.btn:active {
  transform: translateY(1px);
}

/* Navbar Enhancements */
.navbar {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.navbar-brand {
  font-weight: 700;
  transition: all var(--transition-fast);
}

.navbar-brand:hover {
  transform: scale(1.05);
}

/* Toast Container */
.toast-container {
  z-index: 9999;
}

/* Dark Mode Specific Styles */
@media (prefers-color-scheme: dark) {
  .navbar {
    background: rgba(23, 23, 23, 0.95) !important;
  }
  
  .hero-section {
    background: linear-gradient(135deg, var(--neutral-800) 0%, var(--neutral-700) 50%, var(--neutral-900) 100%);
  }
  
  .file-upload-wrapper {
    background: linear-gradient(135deg, var(--neutral-800), var(--neutral-700));
    border-color: var(--neutral-600);
  }
  
  .file-upload-wrapper:hover {
    background: linear-gradient(135deg, var(--neutral-700), var(--neutral-600));
    border-color: var(--primary-500);
  }
}

/* Print Styles */
@media print {
  .navbar,
  .action-section,
  footer {
    display: none !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
  
  .progress-bar {
    background: #007bff !important;
    -webkit-print-color-adjust: exact;
  }
}
