#!/usr/bin/env python3
"""
Debug script to understand what scores are being generated
"""

import requests
import json

def debug_matching():
    """Debug the matching scores"""
    base_url = "http://localhost:8000"
    
    print("🔍 Debugging matching scores...")
    
    # Upload resume
    try:
        with open("test_resume.pdf", "rb") as f:
            files = {"file": ("test_resume.pdf", f, "application/pdf")}
            data = {"name": "Debug Resume Test"}
            
            response = requests.post(f"{base_url}/api/documents/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                resume_id = result["resume_id"]
                print(f"✅ Resume uploaded: {resume_id}")
                
                # Test with very low threshold to see all scores
                response = requests.get(
                    f"{base_url}/api/documents/match",
                    params={
                        "resume_id": resume_id,
                        "threshold": 0.0,  # Very low threshold
                        "limit": 50
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    matches = result.get("matches", [])
                    
                    print(f"\n📊 All scores (threshold 0.0):")
                    print(f"   Found {len(matches)} total matches")
                    
                    if matches:
                        # Sort by score descending
                        matches.sort(key=lambda x: x['match_score'], reverse=True)
                        
                        for i, match in enumerate(matches, 1):
                            score = match['match_score']
                            print(f"   {i:2d}. {match['title'][:40]:<40} | {score:.3f} ({score*100:.1f}%)")
                    else:
                        print("   ❌ No matches found even with threshold 0.0")
                        print("   This indicates a serious issue with the matching algorithm")
                
                else:
                    print(f"❌ Failed to get matches - {response.status_code}")
                    print(f"   Error: {response.text}")
            else:
                print(f"❌ Failed to upload resume - {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_matching()
