<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Results - ResuMatch</title>
    <meta name="description" content="Your AI-powered resume match analysis results with detailed insights and recommendations.">
    
    <!-- Preload Critical Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="/static/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <i class="bi bi-bullseye text-primary me-2" style="font-size: 1.5rem;"></i>
                <span class="fw-bold">ResuMatch</span>
            </a>
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-primary btn-sm me-2" onclick="toggleTheme()" title="Toggle Dark Mode">
                    <i class="bi bi-moon-stars"></i>
                </button>
                <span class="badge bg-success">Analysis Complete</span>
            </div>
        </div>
    </nav>

    <!-- Results Header -->
    <section class="results-header py-5 bg-gradient">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="results-hero">
                        <i class="bi bi-check-circle-fill text-success mb-3" style="font-size: 3rem;"></i>
                        <h1 class="display-5 mb-3">Match Analysis Complete</h1>
                        <p class="lead text-muted">
                            Your resume "{{ resume_filename }}" has been analyzed against the job requirements
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Match Score Section -->
    <section class="match-score-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="card match-score-card">
                        <div class="card-body text-center">
                            <div class="match-score-header mb-4">
                                <h2 class="d-flex align-items-center justify-content-center">
                                    <i class="bi bi-graph-up text-primary me-2"></i>
                                    Overall Match Score
                                </h2>
                                <p class="text-muted">AI-powered compatibility analysis</p>
                            </div>

                            <!-- Main Score Display -->
                            <div class="score-display mb-5">
                                <div class="score-circle">
                                    <div class="score-number">{{ match_percentage }}%</div>
                                    <div class="score-label">{{ match_quality }}</div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="progress-container mb-4">
                                <div class="progress" style="height: 40px;">
                                    {% if match_percentage >= 85 %}
                                        <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: {{ match_percentage }}%;" 
                                             aria-valuenow="{{ match_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ match_percentage }}%
                                        </div>
                                    {% elif match_percentage >= 70 %}
                                        <div class="progress-bar bg-info progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: {{ match_percentage }}%;" 
                                             aria-valuenow="{{ match_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ match_percentage }}%
                                        </div>
                                    {% elif match_percentage >= 50 %}
                                        <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: {{ match_percentage }}%;" 
                                             aria-valuenow="{{ match_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ match_percentage }}%
                                        </div>
                                    {% else %}
                                        <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: {{ match_percentage }}%;" 
                                             aria-valuenow="{{ match_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ match_percentage }}%
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Match Quality Badge -->
                            <div class="quality-badge">
                                {% if match_percentage >= 85 %}
                                    <span class="badge bg-success-subtle text-success px-4 py-2 fs-6">
                                        <i class="bi bi-star-fill me-1"></i>{{ match_quality }}
                                    </span>
                                {% elif match_percentage >= 70 %}
                                    <span class="badge bg-info-subtle text-info px-4 py-2 fs-6">
                                        <i class="bi bi-hand-thumbs-up me-1"></i>{{ match_quality }}
                                    </span>
                                {% elif match_percentage >= 50 %}
                                    <span class="badge bg-warning-subtle text-warning px-4 py-2 fs-6">
                                        <i class="bi bi-exclamation-triangle me-1"></i>{{ match_quality }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger-subtle text-danger px-4 py-2 fs-6">
                                        <i class="bi bi-x-circle me-1"></i>{{ match_quality }}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Detailed Analysis Section -->
    {% if section_percentages %}
    <section class="detailed-analysis py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="d-flex align-items-center mb-0">
                                <i class="bi bi-pie-chart text-primary me-2"></i>
                                Detailed Match Analysis
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                {% for key, value in section_percentages.items() %}
                                    {% if key != 'matching_skills' %}
                                    <div class="col-md-6">
                                        <div class="analysis-item">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h5 class="mb-0">
                                                    {% if key == 'skills_match' %}
                                                        <i class="bi bi-tools me-1"></i>Skills Match
                                                    {% else %}
                                                        <i class="bi bi-graph-up me-1"></i>{{ key|title }}
                                                    {% endif %}
                                                </h5>
                                                <span class="badge bg-primary">{{ value }}%</span>
                                            </div>
                                            <div class="progress mb-3" style="height: 24px;">
                                                {% if value >= 80 %}
                                                    <div class="progress-bar bg-success" role="progressbar" 
                                                         style="width: {{ value }}%;" aria-valuenow="{{ value }}" 
                                                         aria-valuemin="0" aria-valuemax="100">{{ value }}%</div>
                                                {% elif value >= 60 %}
                                                    <div class="progress-bar bg-info" role="progressbar" 
                                                         style="width: {{ value }}%;" aria-valuenow="{{ value }}" 
                                                         aria-valuemin="0" aria-valuemax="100">{{ value }}%</div>
                                                {% elif value >= 40 %}
                                                    <div class="progress-bar bg-warning" role="progressbar" 
                                                         style="width: {{ value }}%;" aria-valuenow="{{ value }}" 
                                                         aria-valuemin="0" aria-valuemax="100">{{ value }}%</div>
                                                {% else %}
                                                    <div class="progress-bar bg-danger" role="progressbar" 
                                                         style="width: {{ value }}%;" aria-valuenow="{{ value }}" 
                                                         aria-valuemin="0" aria-valuemax="100">{{ value }}%</div>
                                                {% endif %}
                                            </div>

                                            {% if key == 'skills_match' and section_scores.matching_skills %}
                                            <div class="matching-skills">
                                                <h6 class="text-muted mb-2">
                                                    <i class="bi bi-check-circle me-1"></i>Matching Skills:
                                                </h6>
                                                <div class="d-flex flex-wrap gap-2">
                                                    {% for skill in section_scores.matching_skills %}
                                                        <span class="badge bg-success-subtle text-success px-3 py-2">
                                                            <i class="bi bi-check me-1"></i>{{ skill|title }}
                                                        </span>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Resume and Job Analysis -->
    <section class="analysis-details py-5">
        <div class="container">
            <div class="row g-4">
                <!-- Resume Analysis -->
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="d-flex align-items-center mb-0">
                                <i class="bi bi-person-badge text-primary me-2"></i>
                                Resume Analysis
                            </h3>
                        </div>
                        <div class="card-body">
                            {% if resume_entities %}
                                <!-- Skills Section -->
                                {% if resume_entities.skills %}
                                <div class="analysis-section mb-4">
                                    <h4 class="d-flex align-items-center">
                                        <i class="bi bi-tools text-success me-2"></i>
                                        Skills Identified
                                        <span class="badge bg-success-subtle text-success ms-2">{{ resume_entities.skills|length }}</span>
                                    </h4>
                                    <div class="skills-grid">
                                        {% for skill in resume_entities.skills %}
                                            <span class="skill-tag">
                                                <i class="bi bi-check-circle me-1"></i>{{ skill }}
                                            </span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Experience Section -->
                                {% if resume_entities.experience %}
                                <div class="analysis-section mb-4">
                                    <h4 class="d-flex align-items-center">
                                        <i class="bi bi-briefcase text-info me-2"></i>
                                        Experience
                                        <span class="badge bg-info-subtle text-info ms-2">{{ resume_entities.experience|length }}</span>
                                    </h4>
                                    <div class="experience-list">
                                        {% for exp in resume_entities.experience %}
                                            <div class="experience-item">
                                                <div class="d-flex align-items-start">
                                                    <i class="bi bi-building text-muted me-2 mt-1"></i>
                                                    <div>
                                                        <h6 class="mb-1">{{ exp.title or 'Position' }}</h6>
                                                        <p class="text-muted mb-1">
                                                            {{ exp.organization or 'Company' }}
                                                            {% if exp.dates %}
                                                                <span class="badge bg-light text-dark ms-2">{{ exp.dates }}</span>
                                                            {% endif %}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Education Section -->
                                {% if resume_entities.education %}
                                <div class="analysis-section">
                                    <h4 class="d-flex align-items-center">
                                        <i class="bi bi-mortarboard text-warning me-2"></i>
                                        Education
                                        <span class="badge bg-warning-subtle text-warning ms-2">{{ resume_entities.education|length }}</span>
                                    </h4>
                                    <div class="education-list">
                                        {% for edu in resume_entities.education %}
                                            <div class="education-item">
                                                <div class="d-flex align-items-start">
                                                    <i class="bi bi-mortarboard text-muted me-2 mt-1"></i>
                                                    <div>
                                                        <h6 class="mb-1">{{ edu.degree or 'Degree' }}</h6>
                                                        <p class="text-muted mb-1">
                                                            {{ edu.institution or 'Institution' }}
                                                            {% if edu.year %}
                                                                <span class="badge bg-light text-dark ms-2">{{ edu.year }}</span>
                                                            {% endif %}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="bi bi-info-circle text-muted mb-2" style="font-size: 2rem;"></i>
                                    <p class="text-muted">Resume analysis data not available</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Job Description -->
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="d-flex align-items-center mb-0">
                                <i class="bi bi-briefcase text-primary me-2"></i>
                                Job Requirements
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="job-description-display">
                                {{ job_description|replace('\n', '<br>')|safe }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recommendations Section -->
    <section class="recommendations py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="d-flex align-items-center mb-0">
                                <i class="bi bi-lightbulb text-primary me-2"></i>
                                AI Recommendations
                            </h3>
                        </div>
                        <div class="card-body">
                            {% if match_percentage >= 85 %}
                                <div class="alert alert-success border-0">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-star-fill text-success me-3 mt-1" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <h4 class="alert-heading">Excellent Match!</h4>
                                            <p class="mb-3">Your resume shows strong alignment with this job opportunity. You have most of the required skills and qualifications.</p>
                                            <hr>
                                            <div class="recommendations-list">
                                                <h6><i class="bi bi-check-circle me-1"></i>Next Steps:</h6>
                                                <ul class="list-unstyled">
                                                    <li><i class="bi bi-arrow-right text-success me-2"></i>Apply with confidence</li>
                                                    <li><i class="bi bi-arrow-right text-success me-2"></i>Prepare for technical interviews</li>
                                                    <li><i class="bi bi-arrow-right text-success me-2"></i>Research the company culture</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% elif match_percentage >= 70 %}
                                <div class="alert alert-info border-0">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-hand-thumbs-up text-info me-3 mt-1" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <h4 class="alert-heading">Good Match</h4>
                                            <p class="mb-3">Your resume demonstrates good compatibility with this role. With some targeted improvements, you could be a strong candidate.</p>
                                            <hr>
                                            <div class="recommendations-list">
                                                <h6><i class="bi bi-gear me-1"></i>Improvement Areas:</h6>
                                                <ul class="list-unstyled">
                                                    <li><i class="bi bi-arrow-right text-info me-2"></i>Highlight relevant project experience</li>
                                                    <li><i class="bi bi-arrow-right text-info me-2"></i>Add missing technical skills</li>
                                                    <li><i class="bi bi-arrow-right text-info me-2"></i>Quantify your achievements</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% elif match_percentage >= 50 %}
                                <div class="alert alert-warning border-0">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-exclamation-triangle text-warning me-3 mt-1" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <h4 class="alert-heading">Fair Match</h4>
                                            <p class="mb-3">Your resume shows some alignment, but significant improvements are needed to be competitive for this role.</p>
                                            <hr>
                                            <div class="recommendations-list">
                                                <h6><i class="bi bi-tools me-1"></i>Focus Areas:</h6>
                                                <ul class="list-unstyled">
                                                    <li><i class="bi bi-arrow-right text-warning me-2"></i>Develop missing core skills</li>
                                                    <li><i class="bi bi-arrow-right text-warning me-2"></i>Gain relevant experience</li>
                                                    <li><i class="bi bi-arrow-right text-warning me-2"></i>Consider additional training or certifications</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="alert alert-danger border-0">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-x-circle text-danger me-3 mt-1" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <h4 class="alert-heading">Limited Match</h4>
                                            <p class="mb-3">Your current resume has limited alignment with this job. Consider significant skill development or targeting more suitable roles.</p>
                                            <hr>
                                            <div class="recommendations-list">
                                                <h6><i class="bi bi-book me-1"></i>Development Path:</h6>
                                                <ul class="list-unstyled">
                                                    <li><i class="bi bi-arrow-right text-danger me-2"></i>Focus on fundamental skill building</li>
                                                    <li><i class="bi bi-arrow-right text-danger me-2"></i>Consider entry-level positions in this field</li>
                                                    <li><i class="bi bi-arrow-right text-danger me-2"></i>Pursue relevant education or training programs</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            {% if section_scores and section_scores.matching_skills %}
                            <div class="mt-4">
                                <h6 class="text-muted">
                                    <i class="bi bi-bullseye me-1"></i>
                                    Leverage your strengths in: 
                                    <strong>{{ section_scores.matching_skills|join(', ')|title }}</strong>
                                </h6>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Action Buttons -->
    <section class="action-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="action-buttons">
                        <a href="/" class="btn btn-primary btn-lg me-3">
                            <i class="bi bi-arrow-left me-2"></i>
                            Try Another Match
                        </a>
                        <button class="btn btn-outline-primary btn-lg" onclick="window.print()">
                            <i class="bi bi-printer me-2"></i>
                            Print Results
                        </button>
                    </div>
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Results are generated using AI analysis and should be used as guidance only
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white border-top">
        <div class="container py-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-bullseye text-primary me-2"></i>
                        <span class="text-muted">ResuMatch - AI-powered Resume to Job Matching</span>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <span class="text-muted me-3">Analysis powered by AI</span>
                        <i class="bi bi-robot text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Theme toggle function
        window.toggleTheme = function() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Update icon
            const themeIcon = document.querySelector('[onclick="toggleTheme()"] i');
            themeIcon.className = newTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
        };

        // Load saved theme and animate elements
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
            const themeIcon = document.querySelector('[onclick="toggleTheme()"] i');
            if (themeIcon) {
                themeIcon.className = savedTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
            }

            // Animate progress bars on load
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });

            // Animate score circle
            const scoreCircle = document.querySelector('.score-circle');
            if (scoreCircle) {
                scoreCircle.style.opacity = '0';
                scoreCircle.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    scoreCircle.style.opacity = '1';
                    scoreCircle.style.transform = 'scale(1)';
                    scoreCircle.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                }, 300);
            }
        });
    </script>

    <style>
        /* Additional styles for results page */
        .score-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-100), var(--accent-100));
            border: 8px solid var(--primary-500);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            position: relative;
            box-shadow: var(--shadow-xl);
        }

        .score-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-700);
            line-height: 1;
        }

        .score-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .skill-tag {
            display: inline-flex;
            align-items: center;
            padding: var(--space-2) var(--space-3);
            background: var(--success-50);
            color: var(--success-700);
            border-radius: var(--radius-full);
            font-size: 0.875rem;
            font-weight: 500;
            margin: var(--space-1);
            border: 1px solid var(--success-200);
        }

        .skills-grid {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .experience-item, .education-item {
            padding: var(--space-3);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-3);
            border-left: 4px solid var(--primary-500);
        }

        .job-description-display {
            max-height: 400px;
            overflow-y: auto;
            padding: var(--space-4);
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            line-height: 1.7;
        }

        @media (max-width: 768px) {
            .score-circle {
                width: 150px;
                height: 150px;
            }
            
            .score-number {
                font-size: 2rem;
            }
        }
    </style>
</body>
</html>
