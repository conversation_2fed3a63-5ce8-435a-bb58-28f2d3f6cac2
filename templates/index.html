<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResuMatch - AI-Powered Resume to Job Matching</title>
    <meta name="description" content="Transform your job search with AI-powered resume matching. Get instant compatibility scores and personalized recommendations.">

    <!-- Preload Critical Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="/static/css/style.css">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <i class="bi bi-bullseye text-primary me-2" style="font-size: 1.5rem;"></i>
                <span class="fw-bold">ResuMatch</span>
            </a>
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-primary btn-sm me-2" onclick="toggleTheme()" title="Toggle Dark Mode">
                    <i class="bi bi-moon-stars"></i>
                </button>
                <span class="badge bg-primary">AI-Powered</span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="hero-content">
                        <h1 class="display-4 mb-4">
                            <i class="bi bi-robot text-primary me-3"></i>
                            ResuMatch
                        </h1>
                        <p class="lead mb-5">
                            Transform your job search with AI-powered resume matching.
                            Get instant compatibility scores, detailed analysis, and personalized recommendations.
                        </p>

                        <!-- Feature Pills -->
                        <div class="d-flex flex-wrap justify-content-center gap-2 mb-5">
                            <span class="badge bg-primary-subtle text-primary px-3 py-2">
                                <i class="bi bi-lightning-charge me-1"></i>Instant Analysis
                            </span>
                            <span class="badge bg-success-subtle text-success px-3 py-2">
                                <i class="bi bi-graph-up me-1"></i>Match Scoring
                            </span>
                            <span class="badge bg-info-subtle text-info px-3 py-2">
                                <i class="bi bi-lightbulb me-1"></i>Smart Recommendations
                            </span>
                            <span class="badge bg-warning-subtle text-warning px-3 py-2">
                                <i class="bi bi-shield-check me-1"></i>Privacy First
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Error Messages -->
    <div id="error-messages" class="container d-none">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="error-text"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form Section -->
    <section class="main-form-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="card main-form-card">
                        <div class="card-body">
                            <form action="/match" method="post" enctype="multipart/form-data" id="match-form">
                                <div class="row g-4">
                                    <!-- Resume Upload Section -->
                                    <div class="col-lg-6">
                                        <div class="upload-section">
                                            <div class="section-header mb-4">
                                                <h3 class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-person text-primary me-2"></i>
                                                    Upload Resume
                                                </h3>
                                                <p class="text-muted mb-0">
                                                    Upload your resume in PDF, DOCX, or TXT format for AI analysis
                                                </p>
                                            </div>

                                            <div class="upload-area">
                                                <label for="resume" class="form-label">Resume File</label>
                                                <div class="file-upload-wrapper">
                                                    <input class="form-control" type="file" id="resume" name="resume"
                                                           accept=".pdf,.docx,.txt" required>
                                                    <div class="file-upload-hint">
                                                        <i class="bi bi-cloud-upload text-primary mb-2" style="font-size: 2rem;"></i>
                                                        <p class="mb-1">Drag & drop your resume here</p>
                                                        <small class="text-muted">or click to browse files</small>
                                                    </div>
                                                </div>

                                                <!-- File Info Display -->
                                                <div id="file-info" class="file-info d-none">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-file-check text-success me-2"></i>
                                                        <span id="file-name"></span>
                                                        <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="clearFile()">
                                                            <i class="bi bi-x"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Job Description Section -->
                                    <div class="col-lg-6">
                                        <div class="job-section">
                                            <div class="section-header mb-4">
                                                <h3 class="d-flex align-items-center">
                                                    <i class="bi bi-briefcase text-primary me-2"></i>
                                                    Job Description
                                                </h3>
                                                <p class="text-muted mb-0">
                                                    Paste the job description or select from our curated examples
                                                </p>
                                            </div>

                                            <!-- Example Jobs Dropdown -->
                                            <div class="mb-4">
                                                <label for="example_jobs" class="form-label">
                                                    <i class="bi bi-collection me-1"></i>
                                                    Example Job Descriptions
                                                </label>
                                                <select class="form-select" id="example_jobs">
                                                    <option value="">-- Select an example job description --</option>
                                                    <option value="software_engineer">
                                                        <i class="bi bi-code-slash"></i> Software Engineer - Full Stack Developer
                                                    </option>
                                                    <option value="data_scientist">
                                                        <i class="bi bi-graph-up"></i> Data Scientist
                                                    </option>
                                                    <option value="marketing_manager">
                                                        <i class="bi bi-megaphone"></i> Marketing Manager
                                                    </option>
                                                    <option value="registered_nurse">
                                                        <i class="bi bi-heart-pulse"></i> Registered Nurse - Medical/Surgical Unit
                                                    </option>
                                                    <option value="administrative_assistant">
                                                        <i class="bi bi-person-workspace"></i> Administrative Assistant
                                                    </option>
                                                    <option value="financial_analyst">
                                                        <i class="bi bi-calculator"></i> Financial Analyst
                                                    </option>
                                                </select>
                                            </div>

                                            <!-- Job Description Textarea -->
                                            <div class="mb-4">
                                                <label for="job_description" class="form-label">
                                                    <i class="bi bi-textarea-t me-1"></i>
                                                    Job Description
                                                </label>
                                                <textarea class="form-control" id="job_description" name="job_description"
                                                          rows="12" required
                                                          placeholder="Paste the complete job description here including requirements, responsibilities, and qualifications..."></textarea>
                                                <div class="form-text">
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    Include all details for better matching accuracy
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="row mt-5">
                                    <div class="col-12 text-center">
                                        <button type="submit" class="btn btn-primary btn-lg px-5" id="submit-btn">
                                            <i class="bi bi-magic me-2"></i>
                                            Analyze Resume Match
                                            <i class="bi bi-arrow-right ms-2"></i>
                                        </button>
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                <i class="bi bi-shield-lock me-1"></i>
                                                Your data is processed securely and not stored permanently
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works-section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="text-center mb-5">
                                <h3 class="d-flex align-items-center justify-content-center">
                                    <i class="bi bi-gear text-primary me-2"></i>
                                    How ResuMatch Works
                                </h3>
                                <p class="text-muted">Advanced AI technology for intelligent resume analysis</p>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-3">
                                    <div class="process-step text-center">
                                        <div class="step-icon">
                                            <i class="bi bi-file-earmark-arrow-up"></i>
                                        </div>
                                        <h5>1. Upload Resume</h5>
                                        <p class="text-muted">Upload your resume in PDF, DOCX, or TXT format</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="process-step text-center">
                                        <div class="step-icon">
                                            <i class="bi bi-cpu"></i>
                                        </div>
                                        <h5>2. AI Analysis</h5>
                                        <p class="text-muted">Our AI extracts skills, experience, and qualifications</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="process-step text-center">
                                        <div class="step-icon">
                                            <i class="bi bi-graph-up-arrow"></i>
                                        </div>
                                        <h5>3. Smart Matching</h5>
                                        <p class="text-muted">Advanced algorithms compare resume to job requirements</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="process-step text-center">
                                        <div class="step-icon">
                                            <i class="bi bi-clipboard-data"></i>
                                        </div>
                                        <h5>4. Get Results</h5>
                                        <p class="text-muted">Receive detailed match score and recommendations</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white border-top">
        <div class="container py-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-bullseye text-primary me-2"></i>
                        <span class="text-muted">ResuMatch - AI-powered Resume to Job Matching</span>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <span class="text-muted me-3">Powered by AI</span>
                        <i class="bi bi-robot text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5>Analyzing Your Resume...</h5>
            <p class="text-muted">Our AI is processing your resume and matching it with the job description</p>
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Modern JavaScript for enhanced UX
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('match-form');
            const errorMessages = document.getElementById('error-messages');
            const errorText = document.getElementById('error-text');
            const exampleJobsSelect = document.getElementById('example_jobs');
            const jobDescriptionTextarea = document.getElementById('job_description');
            const fileInput = document.getElementById('resume');
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const submitBtn = document.getElementById('submit-btn');
            const loadingOverlay = document.getElementById('loading-overlay');

            // File upload handling
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    fileName.textContent = file.name;
                    fileInfo.classList.remove('d-none');

                    // Add success animation
                    fileInfo.style.opacity = '0';
                    setTimeout(() => {
                        fileInfo.style.opacity = '1';
                        fileInfo.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    fileInfo.classList.add('d-none');
                }
            });

            // Clear file function
            window.clearFile = function() {
                fileInput.value = '';
                fileInfo.classList.add('d-none');
            };

            // Example job selection with loading state
            exampleJobsSelect.addEventListener('change', function() {
                const selectedJob = this.value;
                if (selectedJob) {
                    // Show loading state
                    jobDescriptionTextarea.value = "🔄 Loading example job description...";
                    jobDescriptionTextarea.classList.add('loading');

                    // Try multiple endpoints in sequence with improved ad blocker avoidance
                    const tryEndpoints = async () => {
                        // First try the proxy endpoint which is designed to avoid ad blockers
                        try {
                            console.log(`Attempting to fetch from proxy endpoint for ${selectedJob}`);

                            // Use a random query parameter name to further avoid detection
                            const paramNames = ['id', 'ref', 'doc', 'file', 'resource'];
                            const randomParam = paramNames[Math.floor(Math.random() * paramNames.length)];

                            // Add cache-busting and use query parameter approach
                            const cacheBuster = Date.now();
                            const proxyUrl = `/api/proxy/content?${randomParam}=${selectedJob}&_=${cacheBuster}`;

                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 5000);

                            const response = await fetch(proxyUrl, {
                                headers: {
                                    'Accept': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'Pragma': 'no-cache',
                                    'Cache-Control': 'no-cache'
                                },
                                signal: controller.signal,
                                mode: 'cors',
                                credentials: 'omit'
                            });

                            clearTimeout(timeoutId);

                            if (response.ok) {
                                const data = await response.json();
                                if (data.content) {
                                    console.log('Successfully fetched from proxy endpoint');
                                    return { description: data.content };
                                }
                            }
                        } catch (proxyError) {
                            console.warn('Proxy endpoint failed:', proxyError);
                            // Continue to other approaches
                        }

                        // Try the new neutral endpoints that don't trigger ad blockers
                        const neutralEndpoints = [
                            `/api/documents/${selectedJob}`,       // Generic documents endpoint
                            `/api/resources/${selectedJob}`,       // Generic resources endpoint
                        ];

                        for (const endpoint of neutralEndpoints) {
                            try {
                                const cacheBuster = `?_=${Date.now()}`;
                                const url = endpoint + cacheBuster;

                                console.log(`Attempting to fetch from neutral endpoint: ${url}`);

                                const controller = new AbortController();
                                const timeoutId = setTimeout(() => controller.abort(), 5000);

                                const response = await fetch(url, {
                                    headers: {
                                        'Accept': 'application/json',
                                        'X-Requested-With': 'XMLHttpRequest',
                                        'Pragma': 'no-cache',
                                        'Cache-Control': 'no-cache'
                                    },
                                    signal: controller.signal,
                                    mode: 'cors',
                                    credentials: 'omit'
                                });

                                clearTimeout(timeoutId);

                                if (response.ok) {
                                    const data = await response.json();

                                    // Handle different response formats
                                    if (data.text) {
                                        return { description: data.text };
                                    } else if (data.content) {
                                        return { description: data.content };
                                    } else if (data.description) {
                                        return { description: data.description };
                                    }
                                }
                            } catch (err) {
                                console.warn(`Neutral endpoint failed:`, err);
                                // Continue to next endpoint
                            }
                        }

                        // If neutral endpoints failed, try the original endpoints
                        const originalEndpoints = [
                            `/api/data/${selectedJob}`,            // Base64 encoded endpoint
                            `/api/content/${selectedJob}`,         // Generic content endpoint
                            `/api/job-examples/${selectedJob}`,    // Previous endpoint
                            `/api/example-job/${selectedJob}`      // Legacy endpoint
                        ];

                        // Add direct URL endpoints as fallbacks
                        const directEndpoints = [
                            `http://localhost:3001/api/documents/${selectedJob}`,
                            `http://localhost:3001/api/resources/${selectedJob}`,
                            `http://localhost:3001/api/proxy/content?id=${selectedJob}`,
                            `http://localhost:3001/api/data/${selectedJob}`,
                            `http://localhost:3001/api/content/${selectedJob}`
                        ];

                        // Combine all endpoints to try
                        const allEndpoints = [...originalEndpoints, ...directEndpoints];

                        // Headers that might help bypass ad blockers
                        const headers = {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Pragma': 'no-cache',
                            'Cache-Control': 'no-cache'
                        };

                        for (const endpoint of allEndpoints) {
                            try {
                                // Add a cache-busting parameter to avoid cached responses
                                const cacheBuster = `?_=${Date.now()}`;
                                const url = endpoint + cacheBuster;

                                console.log(`Attempting to fetch from fallback endpoint: ${url}`);

                                // Use a timeout to prevent hanging requests
                                const controller = new AbortController();
                                const timeoutPromise = new Promise((_, reject) =>
                                    setTimeout(() => {
                                        controller.abort();
                                        reject(new Error('Request timeout'));
                                    }, 5000)
                                );

                                // Race between fetch and timeout
                                const fetchPromise = fetch(url, {
                                    headers,
                                    signal: controller.signal,
                                    mode: 'cors',
                                    credentials: 'omit'
                                });

                                const response = await Promise.race([fetchPromise, timeoutPromise]);

                                if (response.ok) {
                                    const data = await response.json();

                                    // Handle Base64 encoded response
                                    if (data.encoding === 'base64' && data.encoded) {
                                        try {
                                            const decoded = atob(data.encoded);
                                            return { description: decoded };
                                        } catch (decodeError) {
                                            console.warn('Failed to decode Base64 content:', decodeError);
                                            continue;
                                        }
                                    }

                                    // Handle various response formats
                                    if (data.description) {
                                        return data;
                                    } else if (data.text) {
                                        return { description: data.text };
                                    } else if (data.content) {
                                        return { description: data.content };
                                    }

                                    console.warn('Response missing expected data format:', data);
                                    continue;
                                } else {
                                    console.warn(`Endpoint ${endpoint} returned status: ${response.status}`);
                                }
                            } catch (err) {
                                console.warn(`Failed to fetch from ${endpoint}:`, err);
                                // Continue to next endpoint
                            }
                        }

                        // Hardcoded fallbacks for all job types
                        const fallbacks = {
                            'marketing_manager': `Marketing Manager\n\nCompany: BrandForward Strategies\nLocation: Chicago, IL\nEmployment Type: Full-time\n\nAbout the Role:\nBrandForward Strategies is seeking a creative and results-driven Marketing Manager to lead our marketing efforts. The ideal candidate will have experience developing and implementing marketing strategies across multiple channels, with a focus on digital marketing and brand development. This role will be responsible for growing our brand presence, generating leads, and supporting our sales team.\n\nResponsibilities:\n• Develop and execute comprehensive marketing strategies to achieve business goals\n• Manage digital marketing campaigns across various platforms (social media, email, SEO/SEM)\n• Create and oversee content creation for website, blog, social media, and marketing materials\n• Analyze marketing data to evaluate campaign performance and optimize strategies\n• Manage relationships with external marketing agencies and vendors\n• Collaborate with sales team to develop lead generation strategies\n• Oversee brand consistency across all marketing channels\n• Plan and coordinate participation in industry events and trade shows\n• Manage marketing budget and track ROI of marketing activities`,
                            'software_engineer': `Software Engineer - Full Stack Developer\n\nCompany: TechInnovate Solutions\nLocation: San Francisco, CA\nEmployment Type: Full-time\n\nAbout the Role:\nTechInnovate Solutions is looking for a talented Full Stack Developer to join our engineering team. The ideal candidate will have experience building web applications using modern frameworks and technologies. You will be responsible for developing and maintaining both frontend and backend components of our applications.\n\nResponsibilities:\n• Design and implement new features and functionality\n• Build reusable code and libraries for future use\n• Optimize applications for maximum speed and scalability\n• Collaborate with cross-functional teams to define, design, and ship new features\n• Ensure the technical feasibility of UI/UX designs\n• Help maintain code quality, organization, and automatization`,
                            'data_scientist': `Data Scientist\n\nCompany: DataInsight Analytics\nLocation: Boston, MA\nEmployment Type: Full-time\n\nAbout the Role:\nDataInsight Analytics is seeking a skilled Data Scientist to join our growing team. The ideal candidate will have strong statistical and programming skills, with experience in machine learning and data analysis. You will work on challenging problems and help extract valuable insights from complex datasets.\n\nResponsibilities:\n• Develop and implement machine learning models\n• Analyze large datasets to identify patterns and trends\n• Create data visualizations to communicate findings\n• Collaborate with engineering teams to deploy models to production\n• Stay current with the latest research and technologies in data science\n• Present findings to stakeholders and make recommendations`,
                            'registered_nurse': `Registered Nurse - Medical/Surgical Unit\n\nCompany: HealthCare Medical Center\nLocation: Chicago, IL\nEmployment Type: Full-time\n\nAbout the Role:\nHealthCare Medical Center is seeking a compassionate and skilled Registered Nurse to join our Medical/Surgical Unit. The ideal candidate will have experience in acute care settings and a commitment to providing exceptional patient care. You will be responsible for assessing, planning, implementing, and evaluating patient care.\n\nResponsibilities:\n• Assess patients' health conditions and develop nursing care plans\n• Administer medications and treatments as prescribed\n• Monitor patients' vital signs and report changes in condition\n• Collaborate with interdisciplinary healthcare team members\n• Educate patients and families about health management\n• Maintain accurate and detailed medical records`
                        };

                        if (selectedJob in fallbacks) {
                            console.log(`Using hardcoded fallback for ${selectedJob}`);
                            return { description: fallbacks[selectedJob] };
                        }

                        // Generic fallback if no specific fallback is available
                        return {
                            description: `Position: ${selectedJob.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\nThis is a fallback job description. The actual description could not be loaded due to network issues or content blocking. Please try again later or contact support if the issue persists.`
                        };
                    };

                    // Execute the endpoint sequence
                    tryEndpoints()
                        .then(data => {
                            jobDescriptionTextarea.value = data.description;
                            jobDescriptionTextarea.classList.remove('loading');

                            // Add success feedback
                            showToast('Job description loaded successfully!', 'success');
                        })
                        .catch(error => {
                            console.error('Error loading job description:', error);
                            jobDescriptionTextarea.value = "";
                            jobDescriptionTextarea.classList.remove('loading');
                            showError('Failed to load example job description. Please try again.');
                        });
                }
            });

            // Form submission with loading state
            form.addEventListener('submit', function(e) {
                // Clear previous errors
                hideError();

                // Validation
                const resumeFile = fileInput.files[0];
                const jobDescription = jobDescriptionTextarea.value.trim();

                if (!resumeFile) {
                    e.preventDefault();
                    showError('Please select a resume file.');
                    return false;
                }

                if (!jobDescription) {
                    e.preventDefault();
                    showError('Please enter a job description.');
                    return false;
                }

                // Check file extension
                const fileName = resumeFile.name;
                const fileExt = fileName.split('.').pop().toLowerCase();
                const allowedExts = ['pdf', 'docx', 'txt'];

                if (!allowedExts.includes(fileExt)) {
                    e.preventDefault();
                    showError('Please upload a PDF, DOCX, or TXT file.');
                    return false;
                }

                // Show loading overlay
                showLoading();

                // Simulate progress
                let progress = 0;
                const progressBar = loadingOverlay.querySelector('.progress-bar');
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                }, 500);

                // Clear interval when form actually submits
                setTimeout(() => clearInterval(progressInterval), 100);
            });

            // Utility functions
            function showError(message) {
                errorText.textContent = message;
                errorMessages.classList.remove('d-none');
                errorMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            function hideError() {
                errorMessages.classList.add('d-none');
                errorText.textContent = '';
            }

            function showLoading() {
                loadingOverlay.classList.remove('d-none');
                document.body.style.overflow = 'hidden';
            }

            function hideLoading() {
                loadingOverlay.classList.add('d-none');
                document.body.style.overflow = '';
            }

            function showToast(message, type = 'info') {
                // Create toast element
                const toast = document.createElement('div');
                toast.className = `toast align-items-center text-white bg-${type} border-0`;
                toast.setAttribute('role', 'alert');
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;

                // Add to page
                const toastContainer = document.querySelector('.toast-container') || createToastContainer();
                toastContainer.appendChild(toast);

                // Show toast
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();

                // Remove after hidden
                toast.addEventListener('hidden.bs.toast', () => toast.remove());
            }

            function createToastContainer() {
                const container = document.createElement('div');
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(container);
                return container;
            }

            // Theme toggle function
            window.toggleTheme = function() {
                const html = document.documentElement;
                const currentTheme = html.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                html.setAttribute('data-bs-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                // Update icon
                const themeIcon = document.querySelector('[onclick="toggleTheme()"] i');
                themeIcon.className = newTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
            };

            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
            const themeIcon = document.querySelector('[onclick="toggleTheme()"] i');
            if (themeIcon) {
                themeIcon.className = savedTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
            }
        });
    </script>
</body>
</html>
