#!/usr/bin/env python3
"""
Comprehensive testing script for ResuMatch matching accuracy
"""

import requests
import json
import time
from collections import defaultdict

def upload_test_resume():
    """Upload <PERSON><PERSON><PERSON><PERSON>'s resume for testing"""
    base_url = "http://localhost:8000"
    
    print("🔄 Uploading test resume...")
    
    try:
        with open("test_resume.pdf", "rb") as f:
            files = {"file": ("test_resume.pdf", f, "application/pdf")}
            data = {"name": "<PERSON><PERSON><PERSON><PERSON> Resume - Accuracy Test"}
            
            response = requests.post(f"{base_url}/api/documents/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                resume_id = result["resume_id"]
                print(f"✅ Resume uploaded successfully! Resume ID: {resume_id}")
                
                # Wait for processing
                time.sleep(2)
                
                # Get resume details to verify extraction
                response = requests.get(f"{base_url}/api/resumes/{resume_id}")
                if response.status_code == 200:
                    resume_data = response.json()
                    entities = resume_data.get('entities', {})
                    
                    print(f"\n📋 Resume Analysis:")
                    print(f"   Name: {entities.get('name', 'Not found')}")
                    print(f"   Phone: {entities.get('phone', 'Not found')}")
                    
                    # Show key skills that should match well
                    skills = entities.get('skills', {})
                    key_skills = []
                    for category, skill_list in skills.items():
                        key_skills.extend(skill_list)
                    
                    relevant_skills = [skill for skill in key_skills if skill.lower() in 
                                     ['flutter', 'python', 'dart', 'c++', 'websocket', 'fastapi', 'android', 'firebase']]
                    print(f"   Key Technical Skills: {', '.join(relevant_skills[:8])}")
                    
                    # Show work experience
                    work_exp = entities.get('work_experience', [])
                    if work_exp:
                        print(f"   Work Experience: {work_exp[0].get('title', 'N/A')} at {work_exp[0].get('organization', 'N/A')}")
                
                return resume_id
            else:
                print(f"❌ Failed to upload resume - {response.status_code}")
                return None
                
    except FileNotFoundError:
        print("❌ Resume file not found. Make sure 'test_resume.pdf' exists.")
        return None
    except Exception as e:
        print(f"❌ Error uploading resume: {e}")
        return None

def get_all_jobs():
    """Get all jobs from the database"""
    base_url = "http://localhost:8000"
    
    try:
        response = requests.get(f"{base_url}/api/jobs")
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to get jobs - {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error getting jobs: {e}")
        return []

def categorize_job(job_title):
    """Categorize job by expected match level"""
    title_lower = job_title.lower()
    
    if any(keyword in title_lower for keyword in ['flutter', 'mobile app', 'mobile software']):
        return "HIGH-MATCH"
    elif any(keyword in title_lower for keyword in ['full stack', 'backend', 'frontend', 'software engineer']):
        return "MEDIUM-MATCH"
    elif any(keyword in title_lower for keyword in ['data scientist', 'devops', 'ui/ux', 'qa engineer']):
        return "LOW-MATCH"
    elif any(keyword in title_lower for keyword in ['marketing', 'sales', 'accountant', 'mechanical engineer', 'human resources', 'operations manager']):
        return "VERY LOW-MATCH"
    else:
        return "UNCATEGORIZED"

def test_matching_accuracy(resume_id, jobs):
    """Test matching accuracy across different thresholds"""
    base_url = "http://localhost:8000"
    
    print(f"\n🎯 Testing matching accuracy with {len(jobs)} jobs...")
    
    # Test with multiple thresholds
    thresholds = [0.4, 0.6, 0.8]
    results = {}
    
    for threshold in thresholds:
        print(f"\n--- Testing with threshold: {threshold} ---")
        
        try:
            response = requests.get(
                f"{base_url}/api/documents/match",
                params={
                    "resume_id": resume_id,
                    "threshold": threshold,
                    "limit": 50  # Get all matches
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                matches = result.get("matches", [])
                
                print(f"✅ Found {len(matches)} matches above threshold {threshold}")
                
                # Categorize matches by expected performance
                categorized_matches = defaultdict(list)
                
                for match in matches:
                    job_category = categorize_job(match['title'])
                    categorized_matches[job_category].append({
                        'title': match['title'],
                        'company': match['company'],
                        'score': match['match_score']
                    })
                
                results[threshold] = {
                    'total_matches': len(matches),
                    'categorized_matches': dict(categorized_matches),
                    'all_matches': matches
                }
                
                # Display results by category
                for category in ['HIGH-MATCH', 'MEDIUM-MATCH', 'LOW-MATCH', 'VERY LOW-MATCH']:
                    category_matches = categorized_matches.get(category, [])
                    if category_matches:
                        print(f"\n   {category} Jobs ({len(category_matches)} matches):")
                        for match in sorted(category_matches, key=lambda x: x['score'], reverse=True):
                            score_pct = match['score'] * 100
                            print(f"     • {match['title']}: {match['score']:.3f} ({score_pct:.1f}%)")
                
            else:
                print(f"❌ Failed to get matches - {response.status_code}")
                results[threshold] = None
                
        except Exception as e:
            print(f"❌ Error testing threshold {threshold}: {e}")
            results[threshold] = None
    
    return results

def analyze_results(results, jobs):
    """Analyze the matching results and identify issues"""
    print(f"\n📊 MATCHING ACCURACY ANALYSIS")
    print("=" * 60)
    
    # Create job category mapping
    job_categories = {}
    for job in jobs:
        category = categorize_job(job['title'])
        if category not in job_categories:
            job_categories[category] = []
        job_categories[category].append(job)
    
    print(f"\n📋 Job Distribution:")
    for category, category_jobs in job_categories.items():
        print(f"   {category}: {len(category_jobs)} jobs")
    
    # Analyze results for threshold 0.6 (most balanced)
    threshold_results = results.get(0.6)
    if not threshold_results:
        print("❌ No results available for analysis")
        return
    
    matches = threshold_results['all_matches']
    categorized_matches = threshold_results['categorized_matches']
    
    print(f"\n🎯 Results Analysis (Threshold 0.6):")
    print(f"   Total matches found: {len(matches)}")
    
    # Expected vs Actual performance
    expected_ranges = {
        'HIGH-MATCH': (0.75, 0.85),
        'MEDIUM-MATCH': (0.60, 0.75),
        'LOW-MATCH': (0.40, 0.60),
        'VERY LOW-MATCH': (0.0, 0.40)
    }
    
    issues_found = []
    
    for category, (min_expected, max_expected) in expected_ranges.items():
        category_matches = categorized_matches.get(category, [])
        
        if category_matches:
            scores = [match['score'] for match in category_matches]
            avg_score = sum(scores) / len(scores)
            min_score = min(scores)
            max_score = max(scores)
            
            print(f"\n   {category}:")
            print(f"     Expected range: {min_expected:.2f} - {max_expected:.2f}")
            print(f"     Actual range:   {min_score:.3f} - {max_score:.3f}")
            print(f"     Average score:  {avg_score:.3f}")
            
            # Check if scores are in expected range
            if avg_score < min_expected:
                issues_found.append(f"{category} jobs scoring too low (avg: {avg_score:.3f}, expected: >{min_expected:.2f})")
            elif avg_score > max_expected:
                issues_found.append(f"{category} jobs scoring too high (avg: {avg_score:.3f}, expected: <{max_expected:.2f})")
            
            # Show top matches in this category
            top_matches = sorted(category_matches, key=lambda x: x['score'], reverse=True)[:3]
            print(f"     Top matches:")
            for i, match in enumerate(top_matches, 1):
                print(f"       {i}. {match['title']}: {match['score']:.3f}")
        else:
            print(f"\n   {category}: No matches found")
            if category in ['HIGH-MATCH', 'MEDIUM-MATCH']:
                issues_found.append(f"No {category} jobs found - threshold may be too high")
    
    # Summary of issues
    if issues_found:
        print(f"\n⚠️  ISSUES IDENTIFIED:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
    else:
        print(f"\n✅ All job categories are scoring within expected ranges!")
    
    return issues_found

def main():
    """Main testing function"""
    print("=" * 70)
    print(" 🧪 ResuMatch Matching Accuracy Test")
    print("=" * 70)
    
    # Upload test resume
    resume_id = upload_test_resume()
    if not resume_id:
        print("❌ Cannot proceed without resume")
        return
    
    # Get all jobs
    jobs = get_all_jobs()
    if not jobs:
        print("❌ No jobs found in database")
        return
    
    print(f"\n📊 Testing against {len(jobs)} jobs in database")
    
    # Test matching accuracy
    results = test_matching_accuracy(resume_id, jobs)
    
    # Analyze results and identify issues
    issues = analyze_results(results, jobs)
    
    print(f"\n" + "=" * 70)
    if issues:
        print(" ⚠️  MATCHING ACCURACY NEEDS IMPROVEMENT")
        print("=" * 70)
        print("\n📝 Recommendations:")
        print("   1. Review similarity algorithm weights")
        print("   2. Enhance skill matching logic")
        print("   3. Improve experience level weighting")
        print("   4. Consider domain-specific scoring adjustments")
    else:
        print(" ✅ MATCHING ACCURACY IS EXCELLENT!")
        print("=" * 70)
        print("\n🎉 The ResuMatch system is performing optimally!")
    
    print(f"\n🌐 View detailed API docs at: http://127.0.0.1:8000/docs")

if __name__ == "__main__":
    main()
