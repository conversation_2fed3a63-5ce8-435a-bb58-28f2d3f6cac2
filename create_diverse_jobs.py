#!/usr/bin/env python3
"""
Script to create a diverse set of job descriptions for comprehensive testing
"""

import requests
import json

# Comprehensive job dataset spanning multiple industries and skill levels
diverse_jobs = [
    # HIGH-MATCH JOBS (Expected 75-85%): Flutter/Mobile Development
    {
        "title": "Flutter Developer - Entry Level",
        "company": "MobileTech Innovations",
        "description": """We're seeking an entry-level Flutter Developer to join our mobile development team.
        Perfect opportunity for recent graduates or developers with 1-2 years of experience in mobile app development.

        You'll work on cross-platform mobile applications using Flutter framework, implement state management
        solutions, and integrate with REST APIs. Experience with Dart programming language is essential.""",
        "requirements": """
        - Bachelor's degree in Computer Science or related field
        - 1-2 years of experience with Flutter and Dart
        - Knowledge of mobile app development for Android and iOS
        - Experience with state management (Provider, Riverpod, or BLoC)
        - Understanding of REST API integration
        - Familiarity with Firebase services
        - Basic knowledge of version control (Git)
        """,
        "responsibilities": """
        - Develop cross-platform mobile applications using Flutter
        - Implement UI/UX designs with attention to detail
        - Integrate mobile apps with backend APIs
        - Collaborate with senior developers on code reviews
        - Write clean, maintainable code following best practices
        """,
        "location": "Remote"
    },
    {
        "title": "Senior Mobile App Developer - Flutter",
        "company": "AppCraft Solutions",
        "description": """Senior Mobile App Developer position focusing on Flutter development for enterprise applications.
        We're building next-generation mobile solutions for Fortune 500 companies using cutting-edge technologies.

        The role involves architecting scalable mobile applications, mentoring junior developers, and implementing
        complex features including real-time data synchronization, offline capabilities, and advanced animations.""",
        "requirements": """
        - 5+ years of mobile app development experience
        - Expert-level knowledge of Flutter and Dart
        - Experience with complex state management (BLoC, Riverpod)
        - Knowledge of native Android/iOS development
        - Experience with WebSocket connections and real-time features
        - Familiarity with CI/CD pipelines for mobile apps
        - Leadership and mentoring experience
        """,
        "responsibilities": """
        - Lead mobile app architecture and design decisions
        - Develop complex Flutter applications with advanced features
        - Mentor junior and mid-level developers
        - Implement real-time features and offline synchronization
        - Optimize app performance and memory usage
        - Conduct technical interviews and code reviews
        """,
        "location": "San Francisco, CA"
    },
    {
        "title": "Mobile Software Engineer - Cross Platform",
        "company": "TechFlow Dynamics",
        "description": """Mobile Software Engineer role focusing on cross-platform development using Flutter and React Native.
        Join our team building innovative mobile applications for healthcare and fintech industries.

        You'll work with modern mobile technologies, implement secure authentication systems, and integrate
        with various third-party services. Experience with both Flutter and native development is valued.""",
        "requirements": """
        - 3+ years of mobile development experience
        - Proficiency in Flutter, Dart, and mobile app architecture
        - Experience with React Native is a plus
        - Knowledge of mobile security best practices
        - Experience with Firebase, AWS, or other cloud platforms
        - Understanding of mobile app testing frameworks
        - Experience with app store deployment processes
        """,
        "responsibilities": """
        - Develop and maintain cross-platform mobile applications
        - Implement secure authentication and data handling
        - Collaborate with backend teams for API integration
        - Participate in agile development processes
        - Ensure app performance and user experience optimization
        """,
        "location": "Austin, TX"
    },

    # MEDIUM-MATCH JOBS (Expected 60-75%): Full Stack/Backend with some overlap
    {
        "title": "Full Stack Developer - Python/React",
        "company": "WebSolutions Pro",
        "description": """Full Stack Developer position working with Python backend and React frontend technologies.
        We're building modern web applications with microservices architecture and cloud deployment.

        The role involves developing both frontend and backend components, working with databases, and
        implementing RESTful APIs. Some mobile development experience is a plus but not required.""",
        "requirements": """
        - 3+ years of full stack development experience
        - Strong proficiency in Python (Django/Flask/FastAPI)
        - Experience with React, JavaScript, and modern frontend tools
        - Knowledge of database systems (PostgreSQL, MongoDB)
        - Experience with REST API development
        - Familiarity with cloud platforms (AWS, Azure)
        - Understanding of DevOps practices
        """,
        "responsibilities": """
        - Develop full-stack web applications
        - Design and implement RESTful APIs
        - Create responsive frontend interfaces
        - Work with databases and data modeling
        - Deploy applications to cloud platforms
        """,
        "location": "New York, NY"
    },
    {
        "title": "Backend Developer - Python & APIs",
        "company": "DataFlow Systems",
        "description": """Backend Developer specializing in Python development and API design. We're building
        high-performance backend systems that serve millions of requests daily.

        You'll work with FastAPI, implement microservices architecture, and optimize database performance.
        Experience with real-time systems and WebSocket connections is highly valued.""",
        "requirements": """
        - 4+ years of backend development experience
        - Expert knowledge of Python and FastAPI/Django
        - Experience with database optimization (PostgreSQL, Redis)
        - Knowledge of microservices architecture
        - Experience with WebSocket and real-time systems
        - Familiarity with containerization (Docker, Kubernetes)
        - Understanding of API design and documentation
        """,
        "responsibilities": """
        - Design and develop scalable backend services
        - Implement high-performance APIs using FastAPI
        - Optimize database queries and system performance
        - Build real-time features with WebSocket connections
        - Collaborate with frontend and mobile teams
        """,
        "location": "Seattle, WA"
    },
    {
        "title": "Frontend Developer - React/TypeScript",
        "company": "UI Innovations",
        "description": """Frontend Developer role focusing on React and TypeScript development for modern web applications.
        We create beautiful, responsive user interfaces for enterprise clients.

        The position involves building complex frontend applications, implementing state management, and
        working closely with design teams to create exceptional user experiences.""",
        "requirements": """
        - 3+ years of frontend development experience
        - Strong proficiency in React, TypeScript, and JavaScript
        - Experience with state management (Redux, Context API)
        - Knowledge of modern CSS frameworks and responsive design
        - Familiarity with testing frameworks (Jest, React Testing Library)
        - Understanding of web performance optimization
        - Experience with build tools (Webpack, Vite)
        """,
        "responsibilities": """
        - Develop responsive web applications using React
        - Implement complex UI components and interactions
        - Collaborate with designers and backend developers
        - Optimize application performance and accessibility
        - Write comprehensive tests for frontend components
        """,
        "location": "Boston, MA"
    },
    {
        "title": "Software Engineer - Java/Spring",
        "company": "Enterprise Solutions Inc",
        "description": """Software Engineer position working with Java and Spring framework for enterprise applications.
        We develop large-scale systems for financial services and healthcare industries.

        The role involves building robust backend services, implementing security features, and working
        with distributed systems. Some experience with modern technologies is preferred.""",
        "requirements": """
        - 3+ years of Java development experience
        - Strong knowledge of Spring Framework and Spring Boot
        - Experience with enterprise application development
        - Knowledge of database systems and ORM frameworks
        - Understanding of security best practices
        - Familiarity with microservices architecture
        - Experience with testing frameworks (JUnit, Mockito)
        """,
        "responsibilities": """
        - Develop enterprise Java applications
        - Implement secure and scalable backend services
        - Work with databases and data persistence layers
        - Participate in system architecture decisions
        - Collaborate with cross-functional teams
        """,
        "location": "Chicago, IL"
    },

    # LOW-MATCH JOBS (Expected 40-60%): Related tech but different focus
    {
        "title": "Data Scientist - Machine Learning",
        "company": "AI Analytics Corp",
        "description": """Data Scientist position focusing on machine learning and predictive analytics.
        We work with large datasets to build intelligent systems for various industries.

        The role involves developing ML models, analyzing data patterns, and implementing AI solutions.
        Some programming experience in Python is required, but focus is on data science rather than software development.""",
        "requirements": """
        - Master's degree in Data Science, Statistics, or related field
        - 3+ years of experience in data science and machine learning
        - Proficiency in Python, R, and data analysis libraries
        - Experience with ML frameworks (TensorFlow, PyTorch, scikit-learn)
        - Knowledge of statistical analysis and data visualization
        - Experience with big data tools (Spark, Hadoop)
        - Strong analytical and problem-solving skills
        """,
        "responsibilities": """
        - Develop and deploy machine learning models
        - Analyze large datasets to extract insights
        - Create data visualizations and reports
        - Collaborate with engineering teams on ML integration
        - Research and implement new ML algorithms
        """,
        "location": "Palo Alto, CA"
    },
    {
        "title": "DevOps Engineer - Cloud Infrastructure",
        "company": "CloudOps Solutions",
        "description": """DevOps Engineer role focusing on cloud infrastructure and automation.
        We manage large-scale cloud deployments and implement CI/CD pipelines for development teams.

        The position involves working with AWS, Docker, Kubernetes, and various automation tools.
        Some programming knowledge is helpful but not the primary focus.""",
        "requirements": """
        - 4+ years of DevOps and infrastructure experience
        - Expert knowledge of AWS, Azure, or GCP
        - Experience with containerization (Docker, Kubernetes)
        - Proficiency in infrastructure as code (Terraform, CloudFormation)
        - Knowledge of CI/CD tools (Jenkins, GitLab CI, GitHub Actions)
        - Experience with monitoring and logging tools
        - Scripting skills in Python, Bash, or PowerShell
        """,
        "responsibilities": """
        - Design and manage cloud infrastructure
        - Implement CI/CD pipelines and automation
        - Monitor system performance and reliability
        - Manage containerized applications and orchestration
        - Collaborate with development teams on deployment strategies
        """,
        "location": "Denver, CO"
    },
    {
        "title": "UI/UX Designer - Mobile Apps",
        "company": "Design Studio Pro",
        "description": """UI/UX Designer specializing in mobile app design and user experience.
        We create beautiful, intuitive interfaces for mobile applications across various industries.

        The role involves designing user interfaces, creating prototypes, and working with development
        teams to implement designs. Some understanding of mobile development is beneficial.""",
        "requirements": """
        - 3+ years of UI/UX design experience
        - Strong portfolio of mobile app designs
        - Proficiency in design tools (Figma, Sketch, Adobe Creative Suite)
        - Understanding of mobile design principles and guidelines
        - Experience with prototyping and user testing
        - Knowledge of accessibility and usability best practices
        - Basic understanding of mobile development constraints
        """,
        "responsibilities": """
        - Design user interfaces for mobile applications
        - Create wireframes, prototypes, and design specifications
        - Conduct user research and usability testing
        - Collaborate with developers on design implementation
        - Maintain design systems and style guides
        """,
        "location": "Los Angeles, CA"
    },
    {
        "title": "QA Engineer - Mobile Testing",
        "company": "Quality Assurance Solutions",
        "description": """QA Engineer position focusing on mobile application testing and quality assurance.
        We ensure high-quality mobile apps through comprehensive testing strategies.

        The role involves manual and automated testing of mobile applications, working with development
        teams to identify and resolve issues. Some technical background is required.""",
        "requirements": """
        - 3+ years of QA and testing experience
        - Experience with mobile app testing (iOS and Android)
        - Knowledge of testing frameworks and automation tools
        - Understanding of mobile development lifecycle
        - Experience with bug tracking and test management tools
        - Basic programming skills for test automation
        - Knowledge of performance and security testing
        """,
        "responsibilities": """
        - Develop and execute test plans for mobile applications
        - Perform manual and automated testing
        - Identify, document, and track software defects
        - Collaborate with development teams on quality improvements
        - Maintain test automation frameworks
        """,
        "location": "Portland, OR"
    },

    # VERY LOW-MATCH JOBS (Expected <40%): Completely different fields
    {
        "title": "Marketing Manager - Digital Campaigns",
        "company": "BrandBoost Marketing",
        "description": """Marketing Manager position focusing on digital marketing campaigns and brand strategy.
        We work with clients across various industries to develop comprehensive marketing solutions.

        The role involves campaign planning, social media management, content creation, and performance analysis.
        No technical programming skills required, but digital marketing expertise is essential.""",
        "requirements": """
        - Bachelor's degree in Marketing, Communications, or related field
        - 4+ years of digital marketing experience
        - Experience with marketing automation platforms
        - Knowledge of SEO, SEM, and social media marketing
        - Proficiency in analytics tools (Google Analytics, Facebook Insights)
        - Strong communication and creative skills
        - Experience with content management systems
        """,
        "responsibilities": """
        - Develop and execute digital marketing campaigns
        - Manage social media accounts and content calendars
        - Analyze campaign performance and ROI
        - Collaborate with creative teams on content development
        - Manage marketing budgets and vendor relationships
        """,
        "location": "Miami, FL"
    },
    {
        "title": "Sales Representative - Enterprise Software",
        "company": "SalesForce Solutions",
        "description": """Sales Representative position selling enterprise software solutions to Fortune 500 companies.
        We provide CRM and business automation tools to large organizations.

        The role involves prospecting, relationship building, and closing complex software deals.
        Strong communication skills and business acumen are more important than technical knowledge.""",
        "requirements": """
        - Bachelor's degree in Business, Sales, or related field
        - 3+ years of B2B sales experience
        - Experience selling enterprise software or SaaS solutions
        - Strong presentation and negotiation skills
        - Ability to understand complex business requirements
        - CRM experience (Salesforce, HubSpot)
        - Willingness to travel for client meetings
        """,
        "responsibilities": """
        - Identify and qualify potential enterprise clients
        - Conduct product demonstrations and presentations
        - Negotiate contracts and close deals
        - Maintain relationships with existing clients
        - Collaborate with technical teams on solution design
        """,
        "location": "Dallas, TX"
    },
    {
        "title": "Accountant - Financial Analysis",
        "company": "Financial Services Group",
        "description": """Staff Accountant position focusing on financial analysis and reporting for corporate clients.
        We provide accounting and financial services to mid-size businesses.

        The role involves financial statement preparation, tax compliance, and budget analysis.
        Strong analytical skills and attention to detail are essential.""",
        "requirements": """
        - Bachelor's degree in Accounting or Finance
        - CPA certification preferred
        - 3+ years of accounting experience
        - Proficiency in accounting software (QuickBooks, SAP)
        - Knowledge of GAAP and tax regulations
        - Strong Excel and financial modeling skills
        - Attention to detail and analytical thinking
        """,
        "responsibilities": """
        - Prepare financial statements and reports
        - Conduct monthly and quarterly closes
        - Assist with tax preparation and compliance
        - Perform financial analysis and variance reporting
        - Support audit processes and documentation
        """,
        "location": "Phoenix, AZ"
    },
    {
        "title": "Mechanical Engineer - Product Design",
        "company": "Engineering Innovations",
        "description": """Mechanical Engineer position focusing on product design and development for manufacturing.
        We design mechanical systems and products for automotive and aerospace industries.

        The role involves CAD design, prototyping, testing, and manufacturing support.
        Strong engineering fundamentals and hands-on experience are required.""",
        "requirements": """
        - Bachelor's degree in Mechanical Engineering
        - 4+ years of mechanical design experience
        - Proficiency in CAD software (SolidWorks, AutoCAD)
        - Knowledge of manufacturing processes and materials
        - Experience with FEA and simulation tools
        - Understanding of GD&T and engineering standards
        - Project management skills
        """,
        "responsibilities": """
        - Design mechanical components and systems
        - Create detailed engineering drawings and specifications
        - Conduct design reviews and testing
        - Support manufacturing and quality processes
        - Collaborate with cross-functional engineering teams
        """,
        "location": "Detroit, MI"
    },
    {
        "title": "Human Resources Manager",
        "company": "People Solutions Inc",
        "description": """Human Resources Manager position overseeing all HR functions for a growing technology company.
        We support a diverse workforce across multiple locations and departments.

        The role involves talent acquisition, employee relations, policy development, and compliance management.
        Strong interpersonal skills and HR expertise are essential.""",
        "requirements": """
        - Bachelor's degree in Human Resources or related field
        - 5+ years of HR management experience
        - Knowledge of employment law and regulations
        - Experience with HRIS systems and applicant tracking
        - Strong communication and conflict resolution skills
        - PHR or SHRM certification preferred
        - Experience in technology industry preferred
        """,
        "responsibilities": """
        - Oversee recruitment and hiring processes
        - Manage employee relations and performance issues
        - Develop and implement HR policies and procedures
        - Ensure compliance with employment laws
        - Support organizational development initiatives
        """,
        "location": "Atlanta, GA"
    },
    {
        "title": "Operations Manager - Supply Chain",
        "company": "Logistics Solutions Corp",
        "description": """Operations Manager position overseeing supply chain and logistics operations.
        We manage complex supply chains for retail and manufacturing clients.

        The role involves process optimization, vendor management, and operational efficiency improvements.
        Strong analytical and leadership skills are required.""",
        "requirements": """
        - Bachelor's degree in Operations, Supply Chain, or Business
        - 5+ years of operations management experience
        - Knowledge of supply chain management principles
        - Experience with ERP systems and logistics software
        - Strong analytical and problem-solving skills
        - Lean Six Sigma certification preferred
        - Leadership and team management experience
        """,
        "responsibilities": """
        - Oversee daily operations and logistics processes
        - Manage vendor relationships and contracts
        - Implement process improvements and cost reductions
        - Monitor KPIs and operational metrics
        - Lead cross-functional teams and projects
        """,
        "location": "Memphis, TN"
    }
]

def create_diverse_jobs():
    """Create diverse jobs by calling the API"""
    base_url = "http://localhost:8000"

    print("Creating diverse job descriptions for comprehensive testing...")
    print(f"Total jobs to create: {len(diverse_jobs)}")

    created_jobs = []

    for i, job in enumerate(diverse_jobs, 1):
        try:
            response = requests.post(f"{base_url}/api/documents/job", json=job)
            if response.status_code == 200:
                result = response.json()
                created_jobs.append({
                    "job_id": result['job_id'],
                    "title": job['title'],
                    "company": job['company'],
                    "category": get_job_category(job['title'])
                })
                print(f"✅ Created job {i:2d}: {job['title']} (ID: {result['job_id'][:8]}...)")
            else:
                print(f"❌ Failed to create job {i}: {job['title']} - {response.status_code}")
                print(f"   Error: {response.text}")
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection error - make sure the API server is running on {base_url}")
            break
        except Exception as e:
            print(f"❌ Error creating job {i}: {e}")

    print(f"\n✅ Successfully created {len(created_jobs)} diverse jobs!")

    # Print summary by category
    categories = {}
    for job in created_jobs:
        category = job['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(job)

    print(f"\n📊 Jobs by Expected Match Category:")
    for category, jobs in categories.items():
        print(f"   {category}: {len(jobs)} jobs")
        for job in jobs:
            print(f"     • {job['title']}")

    return created_jobs

def get_job_category(title):
    """Categorize jobs by expected match level"""
    title_lower = title.lower()

    if any(keyword in title_lower for keyword in ['flutter', 'mobile app', 'mobile software']):
        return "HIGH-MATCH (75-85%)"
    elif any(keyword in title_lower for keyword in ['full stack', 'backend', 'frontend', 'software engineer']):
        return "MEDIUM-MATCH (60-75%)"
    elif any(keyword in title_lower for keyword in ['data scientist', 'devops', 'ui/ux', 'qa engineer']):
        return "LOW-MATCH (40-60%)"
    elif any(keyword in title_lower for keyword in ['marketing', 'sales', 'accountant', 'mechanical engineer', 'human resources', 'operations manager']):
        return "VERY LOW-MATCH (<40%)"
    else:
        return "UNCATEGORIZED"

if __name__ == "__main__":
    create_diverse_jobs()
