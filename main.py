import os
import sys
import argparse

from resumatch.api.app import app
from resumatch.utils.helpers import setup_directories, log_event
import uvicorn

def main():
    """Main entry point for the ResuMatch application"""
    parser = argparse.ArgumentParser(description="ResuMatch - AI-powered resume-to-job matching system")
    parser.add_argument('--host', default='0.0.0.0', help='Host to run the API server on')
    parser.add_argument('--port', type=int, default=8000, help='Port to run the API server on')
    parser.add_argument('--reload', action='store_true', help='Enable auto-reload for development')
    parser.add_argument('--download-dataset', action='store_true', help='Download and prepare the resume dataset from Kaggle')
    args = parser.parse_args()

    # Set up necessary directories
    base_path = os.path.dirname(os.path.abspath(__file__))
    directories = setup_directories(base_path)
    
    # Set up logging
    log_file = os.path.join(directories['logs'], 'app.log')
    log_event(log_file, "INFO", f"Starting ResuMatch application on {args.host}:{args.port}")
    
    # Handle dataset download if requested
    if args.download_dataset:
        try:
            from resumatch.utils.dataset_downloader import download_resume_dataset, process_resume_dataset
            print("\n" + "=" * 60)
            print(" 📥 Downloading Resume Dataset from Kaggle")
            print("=" * 60)
            print(" Note: You may need Kaggle API credentials configured.")
            print(" See: https://github.com/Kaggle/kaggle-api#api-credentials")
            print("=" * 60 + "\n")
            
            # Download and process the dataset
            dataset_path = download_resume_dataset()
            if dataset_path:
                output_path = process_resume_dataset(dataset_path)
                if output_path:
                    print("\n" + "=" * 60)
                    print(f" ✅ Dataset successfully processed to: {output_path}")
                    print(" You can now use this data to train your model!")
                    print("=" * 60 + "\n")
                else:
                    print("\n❌ Failed to process the dataset.\n")
            else:
                print("\n❌ Failed to download the dataset.\n")
            
            # Exit after dataset operations are complete
            return
        except:
            pass
    
    # Print startup message
    print("\n" + "=" * 60)
    print(" 🚀 ResuMatch - AI-powered Resume-to-Job Matching System")
    print("=" * 60)
    print(f" 📁 Base directory: {base_path}")
    print(f" 🌐 API available at: http://{args.host}:{args.port}")
    print(f" 📚 Documentation at: http://{args.host}:{args.port}/docs")
    print("=" * 60 + "\n")

    # Run the API server
    uvicorn.run("resumatch.api.app:app", host=args.host, port=args.port, reload=args.reload)

if __name__ == "__main__":
    main()