#!/usr/bin/env python3
"""
<PERSON>ript to create sample job descriptions for testing the ResuMatch application
"""

import requests
import json

# Sample job descriptions that should match well with the provided resume
sample_jobs = [
    {
        "title": "Flutter Developer",
        "company": "TechCorp Solutions",
        "description": """We are looking for a skilled Flutter Developer to join our mobile development team. 
        The ideal candidate will have experience in developing cross-platform mobile applications using Flutter framework.
        
        You will be responsible for developing high-quality mobile applications, optimizing performance, and implementing 
        scalable architectures. Experience with state management solutions like Riverpod is highly valued.
        
        Our tech stack includes Flutter, Dart, Firebase, REST APIs, and modern development practices.""",
        "requirements": """
        - Proficiency in Flutter and Dart programming
        - Experience with mobile app development for iOS and Android
        - Knowledge of state management solutions (Riverpod, Provider, BLoC)
        - Experience with REST APIs and JSON parsing
        - Familiarity with Firebase services
        - Understanding of mobile app performance optimization
        - Experience with version control (Git)
        - Knowledge of app store deployment processes
        """,
        "responsibilities": """
        - Develop and maintain Flutter mobile applications
        - Collaborate with cross-functional teams to define and implement new features
        - Optimize application performance and user experience
        - Write clean, maintainable, and well-documented code
        - Participate in code reviews and technical discussions
        - Stay updated with latest Flutter and mobile development trends
        """,
        "location": "Remote / Hybrid"
    },
    {
        "title": "Software Engineer - Mobile Development",
        "company": "InnovateTech Inc",
        "description": """Join our dynamic team as a Software Engineer focusing on mobile development. 
        We're building next-generation mobile applications using cutting-edge technologies including Flutter, 
        React Native, and native development.
        
        The role involves working on large-scale mobile applications with millions of users, implementing 
        real-time features, and ensuring optimal performance across different devices and platforms.""",
        "requirements": """
        - Bachelor's degree in Computer Science or related field
        - 2+ years of experience in mobile app development
        - Strong proficiency in Flutter, Dart, or React Native
        - Experience with WebSocket connections and real-time data
        - Knowledge of mobile app architecture patterns
        - Familiarity with CI/CD pipelines
        - Experience with app store optimization and deployment
        - Strong problem-solving and debugging skills
        """,
        "responsibilities": """
        - Design and develop mobile applications using Flutter/React Native
        - Implement real-time features and WebSocket connections
        - Optimize app performance and reduce latency
        - Collaborate with backend teams for API integration
        - Participate in agile development processes
        - Mentor junior developers and conduct code reviews
        """,
        "location": "San Francisco, CA"
    },
    {
        "title": "Full Stack Developer",
        "company": "WebSolutions Pro",
        "description": """We're seeking a talented Full Stack Developer to work on innovative web and mobile applications. 
        The ideal candidate will have experience across the entire technology stack, from frontend frameworks 
        to backend services and database management.
        
        You'll be working on projects involving modern web technologies, API development, and mobile app integration. 
        Experience with Python, JavaScript, and mobile frameworks is essential.""",
        "requirements": """
        - Proficiency in Python, JavaScript, and modern web frameworks
        - Experience with frontend frameworks (React, Angular, or Vue.js)
        - Backend development experience with FastAPI, Django, or Flask
        - Knowledge of database systems (PostgreSQL, MongoDB)
        - Experience with mobile development (Flutter, React Native)
        - Familiarity with cloud platforms (AWS, Azure, GCP)
        - Understanding of DevOps practices and containerization
        - Strong communication and teamwork skills
        """,
        "responsibilities": """
        - Develop full-stack web applications using modern technologies
        - Design and implement RESTful APIs and microservices
        - Create responsive and user-friendly frontend interfaces
        - Integrate mobile applications with backend services
        - Optimize application performance and scalability
        - Participate in system architecture decisions
        """,
        "location": "New York, NY"
    },
    {
        "title": "Backend Developer - Python",
        "company": "DataFlow Systems",
        "description": """Looking for an experienced Backend Developer with strong Python skills to join our data-driven 
        platform team. You'll be working on high-performance backend systems that process large volumes of data 
        and serve millions of API requests daily.
        
        The role involves designing scalable architectures, optimizing database queries, and implementing 
        robust API services using FastAPI and modern Python frameworks.""",
        "requirements": """
        - Strong proficiency in Python programming
        - Experience with FastAPI, Django, or Flask frameworks
        - Knowledge of database design and optimization (PostgreSQL, Redis)
        - Experience with API development and documentation
        - Familiarity with containerization (Docker, Kubernetes)
        - Understanding of microservices architecture
        - Experience with message queues and event-driven systems
        - Knowledge of testing frameworks and best practices
        """,
        "responsibilities": """
        - Design and develop scalable backend services using Python
        - Implement high-performance APIs using FastAPI
        - Optimize database queries and data processing pipelines
        - Collaborate with frontend and mobile teams for API integration
        - Implement monitoring and logging solutions
        - Participate in system design and architecture reviews
        """,
        "location": "Austin, TX"
    },
    {
        "title": "Mobile App Developer",
        "company": "AppCraft Studios",
        "description": """Join our creative team as a Mobile App Developer specializing in cross-platform development. 
        We create innovative mobile applications for various industries including healthcare, finance, and entertainment.
        
        The ideal candidate will have experience with Flutter development, state management, and app store optimization. 
        You'll be working on apps with complex UI/UX requirements and real-time data synchronization.""",
        "requirements": """
        - 3+ years of mobile app development experience
        - Expert-level knowledge of Flutter and Dart
        - Experience with state management (BLoC, Riverpod, Provider)
        - Knowledge of mobile app testing and debugging
        - Experience with app store submission and optimization
        - Familiarity with mobile app analytics and crash reporting
        - Understanding of mobile security best practices
        - Experience with continuous integration and deployment
        """,
        "responsibilities": """
        - Develop high-quality mobile applications using Flutter
        - Implement complex UI/UX designs and animations
        - Integrate with various APIs and third-party services
        - Optimize app performance and memory usage
        - Conduct thorough testing and debugging
        - Collaborate with designers and product managers
        """,
        "location": "Seattle, WA"
    }
]

def create_jobs():
    """Create sample jobs by calling the API"""
    base_url = "http://localhost:8000"
    
    print("Creating sample job descriptions...")
    
    for i, job in enumerate(sample_jobs, 1):
        try:
            response = requests.post(f"{base_url}/api/documents/job", json=job)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Created job {i}: {job['title']} (ID: {result['job_id']})")
            else:
                print(f"❌ Failed to create job {i}: {job['title']} - {response.status_code}")
                print(f"   Error: {response.text}")
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection error - make sure the API server is running on {base_url}")
            break
        except Exception as e:
            print(f"❌ Error creating job {i}: {e}")
    
    print(f"\nFinished creating {len(sample_jobs)} sample jobs.")

if __name__ == "__main__":
    create_jobs()
