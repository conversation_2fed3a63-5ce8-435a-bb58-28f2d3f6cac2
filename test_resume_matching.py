#!/usr/bin/env python3
"""
Script to test resume upload and matching functionality
"""

import requests
import json
import time

def test_resume_upload():
    """Test uploading a resume"""
    base_url = "http://localhost:8000"
    
    print("🔄 Testing resume upload...")
    
    # Upload the resume
    try:
        with open("test_resume.pdf", "rb") as f:
            files = {"file": ("test_resume.pdf", f, "application/pdf")}
            data = {"name": "<PERSON><PERSON><PERSON><PERSON> Singh Resume"}
            
            response = requests.post(f"{base_url}/api/documents/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                resume_id = result["resume_id"]
                print(f"✅ Resume uploaded successfully! Resume ID: {resume_id}")
                return resume_id
            else:
                print(f"❌ Failed to upload resume - {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
    except FileNotFoundError:
        print("❌ Resume file not found. Make sure 'test_resume.pdf' exists.")
        return None
    except Exception as e:
        print(f"❌ Error uploading resume: {e}")
        return None

def test_resume_matching(resume_id):
    """Test resume matching with jobs"""
    base_url = "http://localhost:8000"
    
    print(f"\n🔄 Testing resume matching for resume ID: {resume_id}")
    
    try:
        # Test matching with different thresholds
        thresholds = [0.1, 0.3, 0.5, 0.7]
        
        for threshold in thresholds:
            print(f"\n--- Testing with threshold: {threshold} ---")
            
            response = requests.get(
                f"{base_url}/api/documents/match",
                params={
                    "resume_id": resume_id,
                    "threshold": threshold,
                    "limit": 10
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                matches = result.get("matches", [])
                
                print(f"✅ Found {len(matches)} matches with threshold {threshold}")
                
                for i, match in enumerate(matches, 1):
                    print(f"   {i}. {match['title']} at {match['company']}")
                    print(f"      Match Score: {match['match_score']:.2f}")
                    print()
                    
                if not matches:
                    print("   No matches found with this threshold.")
                    
            else:
                print(f"❌ Failed to get matches - {response.status_code}")
                print(f"   Error: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing resume matching: {e}")

def test_get_resume_details(resume_id):
    """Test getting resume details"""
    base_url = "http://localhost:8000"
    
    print(f"\n🔄 Testing resume details retrieval for resume ID: {resume_id}")
    
    try:
        response = requests.get(f"{base_url}/api/resumes/{resume_id}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Resume details retrieved successfully!")
            print(f"   Name: {result.get('name', 'N/A')}")
            print(f"   Uploaded at: {result.get('uploaded_at', 'N/A')}")
            
            # Show first 200 characters of extracted text
            extracted_text = result.get('extracted_text', '')
            if extracted_text:
                print(f"   Extracted text preview: {extracted_text[:200]}...")
            else:
                print("   No extracted text found")
                
        else:
            print(f"❌ Failed to get resume details - {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error getting resume details: {e}")

def test_list_jobs():
    """Test listing all jobs"""
    base_url = "http://localhost:8000"
    
    print(f"\n🔄 Testing job listing...")
    
    try:
        response = requests.get(f"{base_url}/api/jobs")
        
        if response.status_code == 200:
            jobs = response.json()
            print(f"✅ Found {len(jobs)} jobs in the database:")
            
            for i, job in enumerate(jobs, 1):
                print(f"   {i}. {job['title']} at {job['company']}")
                print(f"      Job ID: {job['job_id']}")
                print()
                
        else:
            print(f"❌ Failed to list jobs - {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error listing jobs: {e}")

def main():
    """Main test function"""
    print("=" * 60)
    print(" 🧪 ResuMatch Backend Testing")
    print("=" * 60)
    
    # Test job listing first
    test_list_jobs()
    
    # Test resume upload
    resume_id = test_resume_upload()
    
    if resume_id:
        # Wait a moment for processing
        time.sleep(2)
        
        # Test resume details retrieval
        test_get_resume_details(resume_id)
        
        # Test resume matching
        test_resume_matching(resume_id)
    
    print("\n" + "=" * 60)
    print(" 🏁 Testing Complete")
    print("=" * 60)

if __name__ == "__main__":
    main()
