#!/usr/bin/env python3
"""
Comprehensive test script to demonstrate all ResuMatch functionality
"""

import requests
import json
import time

def test_api_health():
    """Test if the API is running"""
    base_url = "http://localhost:8000"
    
    print("🔄 Testing API health...")
    
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API is running: {result['message']}")
            print(f"   Version: {result['version']}")
            return True
        else:
            print(f"❌ API health check failed - {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API is not accessible: {e}")
        return False

def test_job_management():
    """Test job creation and listing"""
    base_url = "http://localhost:8000"
    
    print("\n🔄 Testing job management...")
    
    # Test listing jobs
    try:
        response = requests.get(f"{base_url}/api/jobs")
        if response.status_code == 200:
            jobs = response.json()
            print(f"✅ Found {len(jobs)} jobs in database")
            
            if jobs:
                # Show details of first job
                first_job = jobs[0]
                print(f"   Sample job: {first_job['title']} at {first_job['company']}")
                
                # Test getting specific job
                job_id = first_job['job_id']
                response = requests.get(f"{base_url}/api/jobs/{job_id}")
                if response.status_code == 200:
                    job_details = response.json()
                    print(f"✅ Successfully retrieved job details for {job_id}")
                else:
                    print(f"❌ Failed to get job details - {response.status_code}")
            
            return len(jobs) > 0
        else:
            print(f"❌ Failed to list jobs - {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing job management: {e}")
        return False

def test_resume_processing():
    """Test resume upload and processing"""
    base_url = "http://localhost:8000"
    
    print("\n🔄 Testing resume processing...")
    
    try:
        with open("test_resume.pdf", "rb") as f:
            files = {"file": ("test_resume.pdf", f, "application/pdf")}
            data = {"name": "Test Resume - Gaurav Singh"}
            
            response = requests.post(f"{base_url}/api/documents/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                resume_id = result["resume_id"]
                print(f"✅ Resume uploaded successfully! Resume ID: {resume_id}")
                
                # Test getting resume details
                time.sleep(1)  # Wait for processing
                response = requests.get(f"{base_url}/api/resumes/{resume_id}")
                
                if response.status_code == 200:
                    resume_details = response.json()
                    print(f"✅ Resume details retrieved successfully")
                    print(f"   Name: {resume_details.get('name', 'N/A')}")
                    
                    extracted_text = resume_details.get('extracted_text', '')
                    if extracted_text:
                        print(f"   Text extracted: {len(extracted_text)} characters")
                        # Show a preview of skills/technologies found
                        text_lower = extracted_text.lower()
                        skills_found = []
                        for skill in ['flutter', 'python', 'dart', 'javascript', 'fastapi', 'websocket']:
                            if skill in text_lower:
                                skills_found.append(skill)
                        if skills_found:
                            print(f"   Skills detected: {', '.join(skills_found)}")
                    
                    return resume_id
                else:
                    print(f"❌ Failed to get resume details - {response.status_code}")
                    return resume_id
            else:
                print(f"❌ Failed to upload resume - {response.status_code}")
                print(f"   Error: {response.text}")
                return None
                
    except FileNotFoundError:
        print("❌ Resume file not found. Make sure 'test_resume.pdf' exists.")
        return None
    except Exception as e:
        print(f"❌ Error testing resume processing: {e}")
        return None

def test_matching_functionality(resume_id):
    """Test the core matching functionality"""
    base_url = "http://localhost:8000"
    
    print(f"\n🔄 Testing matching functionality...")
    
    try:
        # Test with a reasonable threshold
        threshold = 0.6
        response = requests.get(
            f"{base_url}/api/documents/match",
            params={
                "resume_id": resume_id,
                "threshold": threshold,
                "limit": 5
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            matches = result.get("matches", [])
            
            print(f"✅ Found {len(matches)} matches with threshold {threshold}")
            
            if matches:
                print("\n   📊 Top Matches:")
                for i, match in enumerate(matches, 1):
                    score = match['match_score']
                    print(f"   {i}. {match['title']} at {match['company']}")
                    print(f"      Match Score: {score:.2f} ({score*100:.1f}%)")
                    
                    # Categorize the match quality
                    if score >= 0.8:
                        quality = "Excellent"
                    elif score >= 0.7:
                        quality = "Good"
                    elif score >= 0.6:
                        quality = "Fair"
                    else:
                        quality = "Poor"
                    print(f"      Quality: {quality}")
                    print()
                
                # Test different thresholds to show filtering
                print("   🎯 Testing different thresholds:")
                for test_threshold in [0.5, 0.7, 0.8]:
                    response = requests.get(
                        f"{base_url}/api/documents/match",
                        params={
                            "resume_id": resume_id,
                            "threshold": test_threshold,
                            "limit": 10
                        }
                    )
                    if response.status_code == 200:
                        test_result = response.json()
                        test_matches = test_result.get("matches", [])
                        print(f"      Threshold {test_threshold}: {len(test_matches)} matches")
                
                return True
            else:
                print("   No matches found. This might indicate an issue with the matching algorithm.")
                return False
                
        else:
            print(f"❌ Failed to get matches - {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing matching functionality: {e}")
        return False

def test_api_endpoints():
    """Test various API endpoints"""
    base_url = "http://localhost:8000"
    
    print(f"\n🔄 Testing API endpoints...")
    
    endpoints_to_test = [
        ("GET", "/", "Root endpoint"),
        ("GET", "/api/jobs", "List jobs"),
    ]
    
    for method, endpoint, description in endpoints_to_test:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}")
            
            if response.status_code == 200:
                print(f"✅ {description}: OK")
            else:
                print(f"❌ {description}: Failed ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {description}: Error - {e}")

def main():
    """Main test function"""
    print("=" * 70)
    print(" 🧪 ResuMatch Complete Functionality Test")
    print("=" * 70)
    
    # Test API health
    if not test_api_health():
        print("\n❌ API is not running. Please start the server first.")
        return
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test job management
    jobs_available = test_job_management()
    
    if not jobs_available:
        print("\n⚠️  No jobs found. Creating sample jobs...")
        import subprocess
        try:
            subprocess.run(["python", "create_sample_jobs.py"], check=True)
            print("✅ Sample jobs created")
        except Exception as e:
            print(f"❌ Failed to create sample jobs: {e}")
            return
    
    # Test resume processing
    resume_id = test_resume_processing()
    
    if resume_id:
        # Test matching functionality
        matching_success = test_matching_functionality(resume_id)
        
        if matching_success:
            print("\n" + "=" * 70)
            print(" ✅ All tests completed successfully!")
            print(" 🎉 ResuMatch is fully functional and ready to use!")
            print("=" * 70)
            print("\n📋 Summary:")
            print("   • API server is running and responsive")
            print("   • Job management (create, list, retrieve) works")
            print("   • Resume upload and text extraction works")
            print("   • Resume-to-job matching algorithm works")
            print("   • Match scores are realistic and properly formatted")
            print("   • All endpoints are accessible")
            print("\n🌐 Access the API documentation at: http://127.0.0.1:8000/docs")
        else:
            print("\n❌ Matching functionality has issues")
    else:
        print("\n❌ Resume processing failed")

if __name__ == "__main__":
    main()
