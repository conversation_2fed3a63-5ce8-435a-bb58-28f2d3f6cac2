2025-05-24 02:37:28,314 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:37:28,314 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:37:28,315 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:37:28,315 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:37:28,315 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:37:28,333 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:40:50,695 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:40:50,695 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:40:50,695 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:40:50,695 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:40:50,695 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:40:50,732 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:40:51,665 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:40:51,665 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:40:51,665 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:40:51,665 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:40:51,665 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:40:51,668 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:42:57,192 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:42:57,192 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:42:57,192 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:42:57,192 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:42:57,192 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:42:57,210 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:43:40,520 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:43:40,520 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:43:40,520 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:43:40,520 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:43:40,520 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:43:40,525 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:44:01,826 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:44:01,826 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:44:01,826 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:44:01,826 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:44:01,826 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:44:01,831 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:44:57,265 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:44:57,266 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:44:57,266 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:44:57,266 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:44:57,266 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:44:57,270 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:44:58,128 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:44:58,128 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:44:58,128 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:44:58,128 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:44:58,128 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:44:58,130 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:48:34,500 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:48:34,500 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:48:34,500 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:48:34,500 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:48:34,500 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:48:34,505 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:48:50,577 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:48:50,577 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:48:50,577 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:48:50,577 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:48:50,578 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:48:50,582 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:48:51,568 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:48:51,568 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:48:51,568 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:48:51,568 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:48:51,569 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:48:51,571 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:51:53,424 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:51:53,424 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:51:53,424 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:51:53,424 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:51:53,424 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:51:53,429 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:52:05,289 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:52:05,290 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:52:05,290 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:52:05,290 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:52:05,290 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:52:05,294 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:52:14,163 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:52:14,163 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:52:14,163 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:52:14,163 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:52:14,163 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:52:14,173 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:52:35,395 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:52:35,396 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:52:35,396 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:52:35,396 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:52:35,396 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:52:35,400 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:54:57,370 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:54:57,371 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:54:57,371 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:54:57,371 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:54:57,371 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:54:57,375 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:54:58,242 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:54:58,243 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:54:58,243 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:54:58,243 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:54:58,243 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:54:58,245 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:58:42,229 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:58:42,229 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:58:42,229 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:58:42,229 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:58:42,229 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:58:42,234 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:59:13,834 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:59:13,835 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:59:13,835 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:59:13,835 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:59:13,835 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:59:13,839 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:59:28,554 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:59:28,555 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:59:28,555 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:59:28,555 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:59:28,555 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:59:28,559 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:01:28,711 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:01:28,711 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:01:28,711 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:01:28,711 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:01:28,711 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:01:28,728 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:01:29,652 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:01:29,652 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:01:29,652 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:01:29,652 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:01:29,652 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:01:29,655 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:03:47,097 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:03:47,098 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:03:47,098 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:03:47,098 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:03:47,098 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:03:47,116 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:04:22,867 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:04:22,867 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:04:22,867 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:04:22,867 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:04:22,867 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:04:22,873 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:04:32,030 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:04:32,030 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:04:32,030 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:04:32,030 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:04:32,030 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:04:32,034 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:04:56,306 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:04:56,307 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:04:56,307 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:04:56,307 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:04:56,307 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:04:56,312 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:08:10,561 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:08:10,561 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:08:10,561 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:08:10,561 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:08:10,561 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:08:10,585 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:08:29,754 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:08:29,754 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:08:29,754 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:08:29,754 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:08:29,754 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:08:29,759 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:09:29,769 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:09:29,769 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:09:29,769 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:09:29,769 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:09:29,769 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:09:29,773 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:09:46,357 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:09:46,357 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:09:46,357 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:09:46,357 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:09:46,357 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:09:46,362 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:09:47,260 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:09:47,260 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:09:47,260 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:09:47,260 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:09:47,260 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:09:47,263 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:13:47,365 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:13:47,365 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:13:47,365 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:13:47,365 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:13:47,365 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:13:47,383 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:14:15,867 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:14:15,868 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:14:15,868 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:14:15,868 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:14:15,868 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:14:15,872 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:14:56,156 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:14:56,156 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:14:56,156 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:14:56,156 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:14:56,156 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:14:56,161 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:14:57,109 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:14:57,109 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:14:57,109 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:14:57,109 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:14:57,109 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:14:57,112 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:21:17,756 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:21:17,756 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:21:17,756 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:21:17,756 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:21:17,756 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:21:17,762 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:21:29,836 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:21:29,836 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:21:29,836 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:21:29,836 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:21:29,836 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:21:29,841 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:22:16,999 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:22:17,000 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:22:17,000 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:22:17,000 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:22:17,000 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:22:17,004 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:23:04,105 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:23:04,105 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:23:04,105 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:23:04,105 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:23:04,105 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:23:04,111 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:23:18,941 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:23:18,941 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:23:18,941 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:23:18,941 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:23:18,941 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:23:18,945 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:23:19,842 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:23:19,842 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:23:19,842 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:23:19,842 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:23:19,842 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:23:19,845 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:33:27,731 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:33:27,732 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:33:27,732 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:33:27,732 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:33:27,732 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:33:27,750 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:33:34,199 - resumatch-api - ERROR - Content resource not found: 1
2025-05-24 03:37:22,902 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:37:22,902 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:37:22,902 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:37:22,902 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:37:22,902 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:37:22,920 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:40:12,260 - resumatch-api - INFO - Generated resume ID for simple upload: resume_af7d801c
2025-05-24 03:40:12,262 - resumatch-api - INFO - Starting background processing for file: test_resume.txt, resume ID: resume_af7d801c
2025-05-24 03:40:12,262 - resumatch-api - ERROR - Error in background processing for resume resume_af7d801c: I/O operation on closed file.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 846, in process_uploaded_file_background
    await file.seek(0)
  File "/Users/<USER>/Desktop/projects/ResuMatch/.venv/lib/python3.13/site-packages/starlette/datastructures.py", line 453, in seek
    self.file.seek(offset)
    ~~~~~~~~~~~~~~^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py", line 828, in seek
    return self._file.seek(*args)
           ~~~~~~~~~~~~~~~^^^^^^^
ValueError: I/O operation on closed file.
2025-05-24 03:41:22,333 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:41:22,334 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:41:22,334 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:41:22,334 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:41:22,334 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:41:22,351 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:45:48,645 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:45:48,646 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:45:48,646 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:45:48,646 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:45:48,646 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:45:48,662 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:46:07,961 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:46:07,962 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:46:07,962 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:46:07,962 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:46:07,962 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:46:07,966 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:46:22,435 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:46:22,435 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:46:22,435 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:46:22,435 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:46:22,435 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:46:22,439 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:46:50,236 - resumatch-api - INFO - Generated resume ID for simple upload: resume_e2c4789c
2025-05-24 03:46:50,236 - resumatch-api - INFO - Starting background processing for file: software_engineer.txt, resume ID: resume_e2c4789c
2025-05-24 03:46:50,236 - resumatch-api - ERROR - Error in background processing for resume resume_e2c4789c: I/O operation on closed file.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 846, in process_uploaded_file_background
    await file.seek(0)
  File "/opt/homebrew/lib/python3.11/site-packages/starlette/datastructures.py", line 453, in seek
    self.file.seek(offset)
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py", line 825, in seek
    return self._file.seek(*args)
           ^^^^^^^^^^^^^^^^^^^^^^
ValueError: I/O operation on closed file.
2025-05-24 03:47:41,509 - resumatch-api - INFO - Generated resume ID for simple upload: resume_0d365cda
2025-05-24 03:47:41,511 - resumatch-api - INFO - Starting background processing for file: Gaurav Singh Resume.pdf, resume ID: resume_0d365cda
2025-05-24 03:47:41,512 - resumatch-api - ERROR - Error in background processing for resume resume_0d365cda: I/O operation on closed file.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 846, in process_uploaded_file_background
    await file.seek(0)
  File "/opt/homebrew/lib/python3.11/site-packages/starlette/datastructures.py", line 453, in seek
    self.file.seek(offset)
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py", line 825, in seek
    return self._file.seek(*args)
           ^^^^^^^^^^^^^^^^^^^^^^
ValueError: I/O operation on closed file.
2025-05-24 03:47:41,526 - resumatch-api - INFO - Processing job with ID: job_6617ab63
2025-05-24 03:47:41,534 - resumatch-api - INFO - Job embeddings generated for: job_6617ab63
2025-05-24 03:47:41,541 - resumatch-api - INFO - Processing match request for resume resume_0d365cda via path parameter (job_id filter: None)
2025-05-24 03:47:41,541 - resumatch-api - INFO - Matching resume resume_0d365cda with jobs (threshold=0.7, limit=10)
2025-05-24 03:47:41,541 - resumatch-api - ERROR - Embeddings for resume resume_0d365cda not found
2025-05-24 03:47:41,541 - resumatch-api - ERROR - Error in resume matching: 400: Resume embeddings not available. Please try again later.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 1339, in process_resume_matching
    raise HTTPException(status_code=400, detail="Resume embeddings not available. Please try again later.")
fastapi.exceptions.HTTPException: 400: Resume embeddings not available. Please try again later.
2025-05-24 04:05:45,095 - resumatch-api - INFO - Initializing API components...
2025-05-24 04:05:45,095 - resumatch-api - INFO - Document processor initialized
2025-05-24 04:05:45,095 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 04:05:45,095 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 04:05:45,095 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 04:05:45,112 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 14:46:19,905 - resumatch-api - INFO - Initializing API components...
2025-05-24 14:46:19,905 - resumatch-api - INFO - Document processor initialized
2025-05-24 14:46:19,905 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 14:46:19,905 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 14:46:19,906 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 14:46:19,923 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 14:46:20,894 - resumatch-api - INFO - Initializing API components...
2025-05-24 14:46:20,894 - resumatch-api - INFO - Document processor initialized
2025-05-24 14:46:20,894 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 14:46:20,894 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 14:46:20,894 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 14:46:20,898 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:00:52,316 - resumatch-api - INFO - Processing document upload: test-resume.txt
2025-05-24 15:00:52,317 - resumatch-api - INFO - Processing text file: test-resume.txt
2025-05-24 15:00:52,318 - resumatch-api - INFO - Text extraction complete. Extracted 1779 characters
2025-05-24 15:00:52,318 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:00:52,362 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '(*************', 'education': [{'institution': 'Not specified', 'degree': 'ma', 'year': '4567'}, {'institution': 'Not specified', 'degree': 'MMAR', 'year': '2020 - Present'}, {'institution': 'Not specified', 'degree': 'ma', 'year': '2019 - 2020'}, {'institution': 'University', 'degree': 'Not specified', 'year': '2019'}], 'skills': ['Python', 'Java', 'JavaScript', 'TypeScript', 'React', 'Vue.js', 'Node.js', 'Express', 'Django', 'Flask', 'SASS', 'Redux', 'MySQL', 'PostgreSQL', 'MongoDB', 'AWS', 'Azure', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'Agile', 'DevOps', 'Microservices'], 'experience': [{'title': 'Position not specified', 'organization': 'Corp Inc.', 'dates': 'Dates not specified'}], 'projects': [], 'certifications': [], 'languages': [], 'summary': '', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:00:52,362 - resumatch-api - INFO - Generated resume ID: resume_41fb0e81
2025-05-24 15:00:52,362 - resumatch-api - INFO - Resume data stored in database with ID: resume_41fb0e81
2025-05-24 15:00:52,362 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_41fb0e81
2025-05-24 15:00:52,363 - resumatch-api - INFO - Resume processing completed in 0.05 seconds
2025-05-24 15:00:52,364 - resumatch-api - INFO - Starting background embedding generation for resume: resume_41fb0e81
2025-05-24 15:00:52,373 - resumatch-api - INFO - Background embedding generation completed for resume: resume_41fb0e81 in 0.01 seconds
2025-05-24 15:01:05,324 - resumatch-api - INFO - Matching resume resume_41fb0e81 with jobs (threshold=0.5, limit=5)
2025-05-24 15:01:05,324 - resumatch-api - ERROR - No job descriptions available for matching
2025-05-24 15:01:05,325 - resumatch-api - ERROR - Error in resume matching: 404: No job descriptions available for matching
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 1334, in process_resume_matching
    raise HTTPException(status_code=404, detail="No job descriptions available for matching")
fastapi.exceptions.HTTPException: 404: No job descriptions available for matching
2025-05-24 15:01:15,918 - resumatch-api - INFO - Processing job with ID: job_a28fc4c9
2025-05-24 15:01:15,919 - resumatch-api - INFO - Job embeddings generated for: job_a28fc4c9
2025-05-24 15:01:25,785 - resumatch-api - INFO - Matching resume resume_41fb0e81 with jobs (threshold=0.5, limit=5)
2025-05-24 15:01:25,785 - resumatch-api - INFO - Matching resume with 1 jobs
2025-05-24 15:01:25,826 - resumatch-api - INFO - Match completed in 0.04 seconds. Found 1 matches.
2025-05-24 15:08:54,881 - resumatch-api - INFO - Processing document upload: test-upload.txt
2025-05-24 15:08:54,882 - resumatch-api - INFO - Processing text file: test-upload.txt
2025-05-24 15:08:54,882 - resumatch-api - INFO - Text extraction complete. Extracted 170 characters
2025-05-24 15:08:54,882 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:08:54,885 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '', 'education': [{'institution': 'Education details not found in resume', 'degree': 'Not specified', 'year': 'Not specified'}], 'skills': ['Python', 'JavaScript', 'TypeScript', 'React', 'Node.js', 'Django'], 'experience': [{'title': 'Engineer', 'organization': 'Organization not specified', 'dates': 'Dates not specified'}], 'projects': [], 'certifications': [], 'languages': [], 'summary': 'John Doe Software Engineer EXPERIENCE: - JavaScript development - React, Node.js, TypeScript - 5 years of experience SKILLS: - JavaScript, React, Node.js - Python, Django', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:08:54,885 - resumatch-api - INFO - Generated resume ID: resume_82fefc1a
2025-05-24 15:08:54,885 - resumatch-api - INFO - Resume data stored in database with ID: resume_82fefc1a
2025-05-24 15:08:54,885 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_82fefc1a
2025-05-24 15:08:54,885 - resumatch-api - INFO - Resume processing completed in 0.00 seconds
2025-05-24 15:08:54,886 - resumatch-api - INFO - Starting background embedding generation for resume: resume_82fefc1a
2025-05-24 15:08:54,892 - resumatch-api - INFO - Background embedding generation completed for resume: resume_82fefc1a in 0.01 seconds
2025-05-24 15:13:43,704 - resumatch-api - INFO - Processing document upload: test-upload.txt
2025-05-24 15:13:43,704 - resumatch-api - INFO - Processing text file: test-upload.txt
2025-05-24 15:13:43,704 - resumatch-api - INFO - Text extraction complete. Extracted 170 characters
2025-05-24 15:13:43,705 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:13:43,707 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '', 'education': [{'institution': 'Education details not found in resume', 'degree': 'Not specified', 'year': 'Not specified'}], 'skills': ['Python', 'JavaScript', 'TypeScript', 'React', 'Node.js', 'Django'], 'experience': [{'title': 'Engineer', 'organization': 'Organization not specified', 'dates': 'Dates not specified'}], 'projects': [], 'certifications': [], 'languages': [], 'summary': 'John Doe Software Engineer EXPERIENCE: - JavaScript development - React, Node.js, TypeScript - 5 years of experience SKILLS: - JavaScript, React, Node.js - Python, Django', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:13:43,707 - resumatch-api - INFO - Generated resume ID: resume_dd7a5447
2025-05-24 15:13:43,707 - resumatch-api - INFO - Resume data stored in database with ID: resume_dd7a5447
2025-05-24 15:13:43,707 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_dd7a5447
2025-05-24 15:13:43,707 - resumatch-api - INFO - Resume processing completed in 0.00 seconds
2025-05-24 15:13:43,708 - resumatch-api - INFO - Starting background embedding generation for resume: resume_dd7a5447
2025-05-24 15:13:43,708 - resumatch-api - INFO - Background embedding generation completed for resume: resume_dd7a5447 in 0.00 seconds
2025-05-24 15:13:50,316 - resumatch-api - INFO - Processing job with ID: job_4596a496
2025-05-24 15:13:50,317 - resumatch-api - INFO - Job embeddings generated for: job_4596a496
2025-05-24 15:13:56,590 - resumatch-api - INFO - Got resume_id from request body: resume_dd7a5447, job_id: job_4596a496
2025-05-24 15:13:56,590 - resumatch-api - INFO - Matching resume resume_dd7a5447 with jobs (threshold=0.7, limit=10)
2025-05-24 15:13:56,590 - resumatch-api - INFO - Matching resume with 2 jobs
2025-05-24 15:13:56,632 - resumatch-api - INFO - Match completed in 0.04 seconds. Found 2 matches.
2025-05-24 15:14:57,812 - resumatch-api - INFO - Processing document upload: Gaurav Singh Resume.pdf
2025-05-24 15:14:57,813 - resumatch-api - INFO - Processing PDF file: Gaurav Singh Resume.pdf
2025-05-24 15:14:57,902 - resumatch-api - INFO - Text extraction complete. Extracted 3440 characters
2025-05-24 15:14:57,902 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:14:57,919 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '8795347233', 'education': [{'institution': 'Indian Institute of Information Technology, Gwalior, Madhya Pradesh', 'degree': 'Integrated B.Tech and M.Tech in Information Technology', 'year': 'December 2021 - Expected May 2026'}, {'institution': 'Rani Laxmi Bai Memorial, Lucknow, Uttar Pradesh', 'degree': 'All India Senior School Certificate', 'year': 'March 2021'}], 'skills': ['Python', 'Go', 'Dart', 'C', 'SQL', 'Flutter', 'Android', 'Firebase', 'MySQL', 'MongoDB', 'Firebase Firestore', 'Firestore', 'AWS', 'Docker', 'Git', 'GitHub', 'TensorFlow', 'WebSocket', 'JSON', 'S3', 'CLI', 'Google Sign-In', 'Riverpod', 'Play Store', 'Visual Studio Code', 'Android Studio', 'Visual Studio', 'Data Structures and Algorithms', 'Database Management Systems', 'Object-Oriented Programming', 'Operating Systems', 'Computer Networks', 'Authentication'], 'experience': [{'title': 'Flutter Development Intern', 'organization': 'Wormos Corporation', 'dates': 'December 2023 - February 2024'}], 'projects': [{'title': 'Exodus', 'description': 'Cross-platform app with adaptive streaming capabilities', 'technologies': 'Flutter Framework, Python, AWS', 'dates': 'Not specified'}, {'title': 'DeribitXTrader', 'description': "Trading client for Deribit's derivatives exchange using C++17, WebSocket, and JSON parsing", 'technologies': 'C++, C++17, WebSocket, IXWebSocket, JSON, CLI, readline, std::mutex, API', 'dates': 'Not specified'}], 'certifications': [], 'languages': [], 'summary': '', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:14:57,919 - resumatch-api - INFO - Generated resume ID: resume_a5f08a24
2025-05-24 15:14:57,919 - resumatch-api - INFO - Resume data stored in database with ID: resume_a5f08a24
2025-05-24 15:14:57,919 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_a5f08a24
2025-05-24 15:14:57,919 - resumatch-api - INFO - Resume processing completed in 0.11 seconds
2025-05-24 15:14:57,920 - resumatch-api - INFO - Starting background embedding generation for resume: resume_a5f08a24
2025-05-24 15:14:57,927 - resumatch-api - INFO - Background embedding generation completed for resume: resume_a5f08a24 in 0.01 seconds
2025-05-24 15:14:57,928 - resumatch-api - INFO - Processing job with ID: job_1748079897921_software_engineer
2025-05-24 15:14:57,930 - resumatch-api - INFO - Job embeddings generated for: job_1748079897921_software_engineer
2025-05-24 15:14:57,935 - resumatch-api - INFO - Matching resume resume_a5f08a24 with jobs (threshold=0.5, limit=5)
2025-05-24 15:14:57,935 - resumatch-api - INFO - Matching resume with 3 jobs
2025-05-24 15:14:57,972 - resumatch-api - INFO - Match completed in 0.04 seconds. Found 3 matches.
2025-05-24 15:15:15,652 - resumatch-api - INFO - Processing document upload: Gaurav Singh Resume 2.pdf
2025-05-24 15:15:15,652 - resumatch-api - INFO - Processing PDF file: Gaurav Singh Resume 2.pdf
2025-05-24 15:15:15,684 - resumatch-api - INFO - Text extraction complete. Extracted 3393 characters
2025-05-24 15:15:15,684 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:15:15,703 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '8795347233', 'education': [{'institution': 'Indian Institute of Information Technology, Gwalior, Madhya Pradesh', 'degree': 'Integrated B.Tech and M.Tech in Information Technology', 'year': 'December 2021 - Expected May 2026'}, {'institution': 'Rani Laxmi Bai Memorial, Lucknow, Uttar Pradesh', 'degree': 'All India Senior School Certificate', 'year': 'March 2021'}], 'skills': ['Python', 'Go', 'Dart', 'SQL', 'Flutter', 'Android', 'Firebase', 'MySQL', 'MongoDB', 'Firebase Firestore', 'Firestore', 'AWS', 'Docker', 'Git', 'GitHub', 'TensorFlow', 'OAuth', 'S3', 'Google Sign-In', 'Riverpod', 'Play Store', 'Visual Studio Code', 'Android Studio', 'Visual Studio', 'Data Structures and Algorithms', 'Database Management Systems', 'Object-Oriented Programming', 'Operating Systems', 'Computer Networks', 'Accessibility', 'Authentication'], 'experience': [{'title': 'Flutter Development Intern', 'organization': 'Wormos Corporation', 'dates': 'December 2023 - February 2024'}], 'projects': [{'title': 'Exodus', 'description': 'Cross-platform app with adaptive streaming capabilities', 'technologies': 'Flutter Framework, Python, AWS', 'dates': 'Not specified'}], 'certifications': [], 'languages': [], 'summary': '', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:15:15,704 - resumatch-api - INFO - Generated resume ID: resume_3d9230a0
2025-05-24 15:15:15,704 - resumatch-api - INFO - Resume data stored in database with ID: resume_3d9230a0
2025-05-24 15:15:15,704 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_3d9230a0
2025-05-24 15:15:15,704 - resumatch-api - INFO - Resume processing completed in 0.05 seconds
2025-05-24 15:15:15,705 - resumatch-api - INFO - Starting background embedding generation for resume: resume_3d9230a0
2025-05-24 15:15:15,706 - resumatch-api - INFO - Background embedding generation completed for resume: resume_3d9230a0 in 0.00 seconds
2025-05-24 15:15:15,707 - resumatch-api - INFO - Processing job with ID: job_1748079915705_software_engineer
2025-05-24 15:15:15,708 - resumatch-api - INFO - Job embeddings generated for: job_1748079915705_software_engineer
2025-05-24 15:15:15,714 - resumatch-api - INFO - Matching resume resume_3d9230a0 with jobs (threshold=0.5, limit=5)
2025-05-24 15:15:15,714 - resumatch-api - INFO - Matching resume with 4 jobs
2025-05-24 15:15:15,716 - resumatch-api - INFO - Match completed in 0.00 seconds. Found 4 matches.
2025-05-24 15:38:39,756 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:38:39,757 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:38:39,757 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:38:39,757 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:38:39,757 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:38:39,771 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:42:18,765 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:42:18,765 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:42:18,765 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:42:18,765 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:42:18,765 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:42:18,782 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:44:24,187 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:44:24,187 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:44:24,187 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:44:24,187 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:44:24,187 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:44:24,200 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:44:29,778 - resumatch-api - WARNING - Failed to compute embedding for resume f1e9c14e-0f61-4801-ae19-b47cfe0d986b: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:44:29,778 - resumatch-api - INFO - Resume uploaded and processed: f1e9c14e-0f61-4801-ae19-b47cfe0d986b
2025-05-24 15:49:29,711 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:49:29,711 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:49:29,711 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:49:29,711 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:49:29,711 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:49:29,716 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:49:47,440 - resumatch-api - WARNING - Failed to compute embedding for resume 9820b78b-b9e6-4582-92c9-d28c5928b460: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:49:47,440 - resumatch-api - INFO - Resume uploaded and processed: 9820b78b-b9e6-4582-92c9-d28c5928b460
2025-05-24 15:51:58,453 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:51:58,453 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:51:58,453 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:51:58,453 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:51:58,453 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:51:58,458 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:52:02,085 - resumatch-api - WARNING - Failed to compute embedding for resume 09d49528-3b00-4ad0-aa49-133c93201748: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:52:02,085 - resumatch-api - INFO - Resume uploaded and processed: 09d49528-3b00-4ad0-aa49-133c93201748
2025-05-24 15:52:43,886 - resumatch-api - ERROR - Failed to recompute embedding for resume 09d49528-3b00-4ad0-aa49-133c93201748: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:53:46,041 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:53:46,041 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:53:46,041 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:53:46,041 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:53:46,041 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:53:46,045 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:53:52,912 - resumatch-api - WARNING - Failed to compute embedding for resume 54741361-9f2b-4546-bd30-213d1c3129da: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:53:52,913 - resumatch-api - INFO - Resume uploaded and processed: 54741361-9f2b-4546-bd30-213d1c3129da
2025-05-24 15:54:21,267 - resumatch-api - INFO - Recomputing embedding for resume 54741361-9f2b-4546-bd30-213d1c3129da. Extracted text length: 3440
2025-05-24 15:54:21,268 - resumatch-api - ERROR - Failed to recompute embedding for resume 54741361-9f2b-4546-bd30-213d1c3129da: 'TextEmbedder' object has no attribute 'get_embedding'
Extracted text: Gaurav Singh Email : gaurav91345gmail.com Mobile : 91 8795347233 LinkedIn: gaurav-singh Leetcode: gaurav91345 Github: gaurav-singh7092 Summary Software Developer with experience in developing user-cen...
2025-05-24 15:56:26,541 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:56:26,541 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:56:26,541 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:56:26,541 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:56:26,541 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:56:26,545 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
