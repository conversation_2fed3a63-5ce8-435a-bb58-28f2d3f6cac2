2025-05-24 02:37:28,314 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:37:28,314 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:37:28,315 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:37:28,315 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:37:28,315 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:37:28,333 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:40:50,695 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:40:50,695 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:40:50,695 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:40:50,695 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:40:50,695 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:40:50,732 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:40:51,665 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:40:51,665 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:40:51,665 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:40:51,665 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:40:51,665 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:40:51,668 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:42:57,192 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:42:57,192 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:42:57,192 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:42:57,192 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:42:57,192 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:42:57,210 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:43:40,520 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:43:40,520 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:43:40,520 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:43:40,520 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:43:40,520 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:43:40,525 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:44:01,826 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:44:01,826 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:44:01,826 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:44:01,826 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:44:01,826 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:44:01,831 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:44:57,265 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:44:57,266 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:44:57,266 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:44:57,266 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:44:57,266 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:44:57,270 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:44:58,128 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:44:58,128 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:44:58,128 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:44:58,128 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:44:58,128 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:44:58,130 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:48:34,500 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:48:34,500 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:48:34,500 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:48:34,500 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:48:34,500 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:48:34,505 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:48:50,577 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:48:50,577 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:48:50,577 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:48:50,577 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:48:50,578 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:48:50,582 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:48:51,568 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:48:51,568 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:48:51,568 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:48:51,568 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:48:51,569 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:48:51,571 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:51:53,424 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:51:53,424 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:51:53,424 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:51:53,424 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:51:53,424 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:51:53,429 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:52:05,289 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:52:05,290 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:52:05,290 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:52:05,290 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:52:05,290 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:52:05,294 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:52:14,163 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:52:14,163 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:52:14,163 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:52:14,163 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:52:14,163 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:52:14,173 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:52:35,395 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:52:35,396 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:52:35,396 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:52:35,396 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:52:35,396 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:52:35,400 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:54:57,370 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:54:57,371 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:54:57,371 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:54:57,371 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:54:57,371 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:54:57,375 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:54:58,242 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:54:58,243 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:54:58,243 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:54:58,243 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:54:58,243 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:54:58,245 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:58:42,229 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:58:42,229 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:58:42,229 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:58:42,229 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:58:42,229 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:58:42,234 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:59:13,834 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:59:13,835 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:59:13,835 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:59:13,835 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:59:13,835 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:59:13,839 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 02:59:28,554 - resumatch-api - INFO - Initializing API components...
2025-05-24 02:59:28,555 - resumatch-api - INFO - Document processor initialized
2025-05-24 02:59:28,555 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 02:59:28,555 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 02:59:28,555 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 02:59:28,559 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:01:28,711 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:01:28,711 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:01:28,711 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:01:28,711 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:01:28,711 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:01:28,728 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:01:29,652 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:01:29,652 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:01:29,652 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:01:29,652 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:01:29,652 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:01:29,655 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:03:47,097 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:03:47,098 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:03:47,098 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:03:47,098 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:03:47,098 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:03:47,116 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:04:22,867 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:04:22,867 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:04:22,867 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:04:22,867 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:04:22,867 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:04:22,873 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:04:32,030 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:04:32,030 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:04:32,030 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:04:32,030 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:04:32,030 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:04:32,034 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:04:56,306 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:04:56,307 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:04:56,307 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:04:56,307 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:04:56,307 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:04:56,312 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:08:10,561 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:08:10,561 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:08:10,561 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:08:10,561 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:08:10,561 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:08:10,585 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:08:29,754 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:08:29,754 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:08:29,754 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:08:29,754 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:08:29,754 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:08:29,759 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:09:29,769 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:09:29,769 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:09:29,769 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:09:29,769 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:09:29,769 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:09:29,773 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:09:46,357 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:09:46,357 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:09:46,357 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:09:46,357 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:09:46,357 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:09:46,362 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:09:47,260 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:09:47,260 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:09:47,260 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:09:47,260 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:09:47,260 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:09:47,263 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:13:47,365 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:13:47,365 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:13:47,365 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:13:47,365 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:13:47,365 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:13:47,383 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:14:15,867 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:14:15,868 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:14:15,868 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:14:15,868 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:14:15,868 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:14:15,872 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:14:56,156 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:14:56,156 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:14:56,156 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:14:56,156 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:14:56,156 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:14:56,161 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:14:57,109 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:14:57,109 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:14:57,109 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:14:57,109 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:14:57,109 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:14:57,112 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:21:17,756 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:21:17,756 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:21:17,756 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:21:17,756 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:21:17,756 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:21:17,762 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:21:29,836 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:21:29,836 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:21:29,836 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:21:29,836 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:21:29,836 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:21:29,841 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:22:16,999 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:22:17,000 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:22:17,000 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:22:17,000 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:22:17,000 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:22:17,004 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:23:04,105 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:23:04,105 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:23:04,105 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:23:04,105 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:23:04,105 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:23:04,111 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:23:18,941 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:23:18,941 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:23:18,941 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:23:18,941 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:23:18,941 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:23:18,945 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:23:19,842 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:23:19,842 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:23:19,842 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:23:19,842 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:23:19,842 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:23:19,845 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:33:27,731 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:33:27,732 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:33:27,732 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:33:27,732 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:33:27,732 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:33:27,750 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:33:34,199 - resumatch-api - ERROR - Content resource not found: 1
2025-05-24 03:37:22,902 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:37:22,902 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:37:22,902 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:37:22,902 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:37:22,902 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:37:22,920 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:40:12,260 - resumatch-api - INFO - Generated resume ID for simple upload: resume_af7d801c
2025-05-24 03:40:12,262 - resumatch-api - INFO - Starting background processing for file: test_resume.txt, resume ID: resume_af7d801c
2025-05-24 03:40:12,262 - resumatch-api - ERROR - Error in background processing for resume resume_af7d801c: I/O operation on closed file.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 846, in process_uploaded_file_background
    await file.seek(0)
  File "/Users/<USER>/Desktop/projects/ResuMatch/.venv/lib/python3.13/site-packages/starlette/datastructures.py", line 453, in seek
    self.file.seek(offset)
    ~~~~~~~~~~~~~~^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py", line 828, in seek
    return self._file.seek(*args)
           ~~~~~~~~~~~~~~~^^^^^^^
ValueError: I/O operation on closed file.
2025-05-24 03:41:22,333 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:41:22,334 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:41:22,334 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:41:22,334 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:41:22,334 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:41:22,351 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:45:48,645 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:45:48,646 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:45:48,646 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:45:48,646 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:45:48,646 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:45:48,662 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:46:07,961 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:46:07,962 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:46:07,962 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:46:07,962 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:46:07,962 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:46:07,966 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:46:22,435 - resumatch-api - INFO - Initializing API components...
2025-05-24 03:46:22,435 - resumatch-api - INFO - Document processor initialized
2025-05-24 03:46:22,435 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 03:46:22,435 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 03:46:22,435 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 03:46:22,439 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 03:46:50,236 - resumatch-api - INFO - Generated resume ID for simple upload: resume_e2c4789c
2025-05-24 03:46:50,236 - resumatch-api - INFO - Starting background processing for file: software_engineer.txt, resume ID: resume_e2c4789c
2025-05-24 03:46:50,236 - resumatch-api - ERROR - Error in background processing for resume resume_e2c4789c: I/O operation on closed file.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 846, in process_uploaded_file_background
    await file.seek(0)
  File "/opt/homebrew/lib/python3.11/site-packages/starlette/datastructures.py", line 453, in seek
    self.file.seek(offset)
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py", line 825, in seek
    return self._file.seek(*args)
           ^^^^^^^^^^^^^^^^^^^^^^
ValueError: I/O operation on closed file.
2025-05-24 03:47:41,509 - resumatch-api - INFO - Generated resume ID for simple upload: resume_0d365cda
2025-05-24 03:47:41,511 - resumatch-api - INFO - Starting background processing for file: Gaurav Singh Resume.pdf, resume ID: resume_0d365cda
2025-05-24 03:47:41,512 - resumatch-api - ERROR - Error in background processing for resume resume_0d365cda: I/O operation on closed file.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 846, in process_uploaded_file_background
    await file.seek(0)
  File "/opt/homebrew/lib/python3.11/site-packages/starlette/datastructures.py", line 453, in seek
    self.file.seek(offset)
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py", line 825, in seek
    return self._file.seek(*args)
           ^^^^^^^^^^^^^^^^^^^^^^
ValueError: I/O operation on closed file.
2025-05-24 03:47:41,526 - resumatch-api - INFO - Processing job with ID: job_6617ab63
2025-05-24 03:47:41,534 - resumatch-api - INFO - Job embeddings generated for: job_6617ab63
2025-05-24 03:47:41,541 - resumatch-api - INFO - Processing match request for resume resume_0d365cda via path parameter (job_id filter: None)
2025-05-24 03:47:41,541 - resumatch-api - INFO - Matching resume resume_0d365cda with jobs (threshold=0.7, limit=10)
2025-05-24 03:47:41,541 - resumatch-api - ERROR - Embeddings for resume resume_0d365cda not found
2025-05-24 03:47:41,541 - resumatch-api - ERROR - Error in resume matching: 400: Resume embeddings not available. Please try again later.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 1339, in process_resume_matching
    raise HTTPException(status_code=400, detail="Resume embeddings not available. Please try again later.")
fastapi.exceptions.HTTPException: 400: Resume embeddings not available. Please try again later.
2025-05-24 04:05:45,095 - resumatch-api - INFO - Initializing API components...
2025-05-24 04:05:45,095 - resumatch-api - INFO - Document processor initialized
2025-05-24 04:05:45,095 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 04:05:45,095 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 04:05:45,095 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 04:05:45,112 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 14:46:19,905 - resumatch-api - INFO - Initializing API components...
2025-05-24 14:46:19,905 - resumatch-api - INFO - Document processor initialized
2025-05-24 14:46:19,905 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 14:46:19,905 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 14:46:19,906 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 14:46:19,923 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 14:46:20,894 - resumatch-api - INFO - Initializing API components...
2025-05-24 14:46:20,894 - resumatch-api - INFO - Document processor initialized
2025-05-24 14:46:20,894 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 14:46:20,894 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 14:46:20,894 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 14:46:20,898 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:00:52,316 - resumatch-api - INFO - Processing document upload: test-resume.txt
2025-05-24 15:00:52,317 - resumatch-api - INFO - Processing text file: test-resume.txt
2025-05-24 15:00:52,318 - resumatch-api - INFO - Text extraction complete. Extracted 1779 characters
2025-05-24 15:00:52,318 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:00:52,362 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '(*************', 'education': [{'institution': 'Not specified', 'degree': 'ma', 'year': '4567'}, {'institution': 'Not specified', 'degree': 'MMAR', 'year': '2020 - Present'}, {'institution': 'Not specified', 'degree': 'ma', 'year': '2019 - 2020'}, {'institution': 'University', 'degree': 'Not specified', 'year': '2019'}], 'skills': ['Python', 'Java', 'JavaScript', 'TypeScript', 'React', 'Vue.js', 'Node.js', 'Express', 'Django', 'Flask', 'SASS', 'Redux', 'MySQL', 'PostgreSQL', 'MongoDB', 'AWS', 'Azure', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'Agile', 'DevOps', 'Microservices'], 'experience': [{'title': 'Position not specified', 'organization': 'Corp Inc.', 'dates': 'Dates not specified'}], 'projects': [], 'certifications': [], 'languages': [], 'summary': '', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:00:52,362 - resumatch-api - INFO - Generated resume ID: resume_41fb0e81
2025-05-24 15:00:52,362 - resumatch-api - INFO - Resume data stored in database with ID: resume_41fb0e81
2025-05-24 15:00:52,362 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_41fb0e81
2025-05-24 15:00:52,363 - resumatch-api - INFO - Resume processing completed in 0.05 seconds
2025-05-24 15:00:52,364 - resumatch-api - INFO - Starting background embedding generation for resume: resume_41fb0e81
2025-05-24 15:00:52,373 - resumatch-api - INFO - Background embedding generation completed for resume: resume_41fb0e81 in 0.01 seconds
2025-05-24 15:01:05,324 - resumatch-api - INFO - Matching resume resume_41fb0e81 with jobs (threshold=0.5, limit=5)
2025-05-24 15:01:05,324 - resumatch-api - ERROR - No job descriptions available for matching
2025-05-24 15:01:05,325 - resumatch-api - ERROR - Error in resume matching: 404: No job descriptions available for matching
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/router.py", line 1334, in process_resume_matching
    raise HTTPException(status_code=404, detail="No job descriptions available for matching")
fastapi.exceptions.HTTPException: 404: No job descriptions available for matching
2025-05-24 15:01:15,918 - resumatch-api - INFO - Processing job with ID: job_a28fc4c9
2025-05-24 15:01:15,919 - resumatch-api - INFO - Job embeddings generated for: job_a28fc4c9
2025-05-24 15:01:25,785 - resumatch-api - INFO - Matching resume resume_41fb0e81 with jobs (threshold=0.5, limit=5)
2025-05-24 15:01:25,785 - resumatch-api - INFO - Matching resume with 1 jobs
2025-05-24 15:01:25,826 - resumatch-api - INFO - Match completed in 0.04 seconds. Found 1 matches.
2025-05-24 15:08:54,881 - resumatch-api - INFO - Processing document upload: test-upload.txt
2025-05-24 15:08:54,882 - resumatch-api - INFO - Processing text file: test-upload.txt
2025-05-24 15:08:54,882 - resumatch-api - INFO - Text extraction complete. Extracted 170 characters
2025-05-24 15:08:54,882 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:08:54,885 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '', 'education': [{'institution': 'Education details not found in resume', 'degree': 'Not specified', 'year': 'Not specified'}], 'skills': ['Python', 'JavaScript', 'TypeScript', 'React', 'Node.js', 'Django'], 'experience': [{'title': 'Engineer', 'organization': 'Organization not specified', 'dates': 'Dates not specified'}], 'projects': [], 'certifications': [], 'languages': [], 'summary': 'John Doe Software Engineer EXPERIENCE: - JavaScript development - React, Node.js, TypeScript - 5 years of experience SKILLS: - JavaScript, React, Node.js - Python, Django', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:08:54,885 - resumatch-api - INFO - Generated resume ID: resume_82fefc1a
2025-05-24 15:08:54,885 - resumatch-api - INFO - Resume data stored in database with ID: resume_82fefc1a
2025-05-24 15:08:54,885 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_82fefc1a
2025-05-24 15:08:54,885 - resumatch-api - INFO - Resume processing completed in 0.00 seconds
2025-05-24 15:08:54,886 - resumatch-api - INFO - Starting background embedding generation for resume: resume_82fefc1a
2025-05-24 15:08:54,892 - resumatch-api - INFO - Background embedding generation completed for resume: resume_82fefc1a in 0.01 seconds
2025-05-24 15:13:43,704 - resumatch-api - INFO - Processing document upload: test-upload.txt
2025-05-24 15:13:43,704 - resumatch-api - INFO - Processing text file: test-upload.txt
2025-05-24 15:13:43,704 - resumatch-api - INFO - Text extraction complete. Extracted 170 characters
2025-05-24 15:13:43,705 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:13:43,707 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '', 'education': [{'institution': 'Education details not found in resume', 'degree': 'Not specified', 'year': 'Not specified'}], 'skills': ['Python', 'JavaScript', 'TypeScript', 'React', 'Node.js', 'Django'], 'experience': [{'title': 'Engineer', 'organization': 'Organization not specified', 'dates': 'Dates not specified'}], 'projects': [], 'certifications': [], 'languages': [], 'summary': 'John Doe Software Engineer EXPERIENCE: - JavaScript development - React, Node.js, TypeScript - 5 years of experience SKILLS: - JavaScript, React, Node.js - Python, Django', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:13:43,707 - resumatch-api - INFO - Generated resume ID: resume_dd7a5447
2025-05-24 15:13:43,707 - resumatch-api - INFO - Resume data stored in database with ID: resume_dd7a5447
2025-05-24 15:13:43,707 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_dd7a5447
2025-05-24 15:13:43,707 - resumatch-api - INFO - Resume processing completed in 0.00 seconds
2025-05-24 15:13:43,708 - resumatch-api - INFO - Starting background embedding generation for resume: resume_dd7a5447
2025-05-24 15:13:43,708 - resumatch-api - INFO - Background embedding generation completed for resume: resume_dd7a5447 in 0.00 seconds
2025-05-24 15:13:50,316 - resumatch-api - INFO - Processing job with ID: job_4596a496
2025-05-24 15:13:50,317 - resumatch-api - INFO - Job embeddings generated for: job_4596a496
2025-05-24 15:13:56,590 - resumatch-api - INFO - Got resume_id from request body: resume_dd7a5447, job_id: job_4596a496
2025-05-24 15:13:56,590 - resumatch-api - INFO - Matching resume resume_dd7a5447 with jobs (threshold=0.7, limit=10)
2025-05-24 15:13:56,590 - resumatch-api - INFO - Matching resume with 2 jobs
2025-05-24 15:13:56,632 - resumatch-api - INFO - Match completed in 0.04 seconds. Found 2 matches.
2025-05-24 15:14:57,812 - resumatch-api - INFO - Processing document upload: Gaurav Singh Resume.pdf
2025-05-24 15:14:57,813 - resumatch-api - INFO - Processing PDF file: Gaurav Singh Resume.pdf
2025-05-24 15:14:57,902 - resumatch-api - INFO - Text extraction complete. Extracted 3440 characters
2025-05-24 15:14:57,902 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:14:57,919 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '8795347233', 'education': [{'institution': 'Indian Institute of Information Technology, Gwalior, Madhya Pradesh', 'degree': 'Integrated B.Tech and M.Tech in Information Technology', 'year': 'December 2021 - Expected May 2026'}, {'institution': 'Rani Laxmi Bai Memorial, Lucknow, Uttar Pradesh', 'degree': 'All India Senior School Certificate', 'year': 'March 2021'}], 'skills': ['Python', 'Go', 'Dart', 'C', 'SQL', 'Flutter', 'Android', 'Firebase', 'MySQL', 'MongoDB', 'Firebase Firestore', 'Firestore', 'AWS', 'Docker', 'Git', 'GitHub', 'TensorFlow', 'WebSocket', 'JSON', 'S3', 'CLI', 'Google Sign-In', 'Riverpod', 'Play Store', 'Visual Studio Code', 'Android Studio', 'Visual Studio', 'Data Structures and Algorithms', 'Database Management Systems', 'Object-Oriented Programming', 'Operating Systems', 'Computer Networks', 'Authentication'], 'experience': [{'title': 'Flutter Development Intern', 'organization': 'Wormos Corporation', 'dates': 'December 2023 - February 2024'}], 'projects': [{'title': 'Exodus', 'description': 'Cross-platform app with adaptive streaming capabilities', 'technologies': 'Flutter Framework, Python, AWS', 'dates': 'Not specified'}, {'title': 'DeribitXTrader', 'description': "Trading client for Deribit's derivatives exchange using C++17, WebSocket, and JSON parsing", 'technologies': 'C++, C++17, WebSocket, IXWebSocket, JSON, CLI, readline, std::mutex, API', 'dates': 'Not specified'}], 'certifications': [], 'languages': [], 'summary': '', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:14:57,919 - resumatch-api - INFO - Generated resume ID: resume_a5f08a24
2025-05-24 15:14:57,919 - resumatch-api - INFO - Resume data stored in database with ID: resume_a5f08a24
2025-05-24 15:14:57,919 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_a5f08a24
2025-05-24 15:14:57,919 - resumatch-api - INFO - Resume processing completed in 0.11 seconds
2025-05-24 15:14:57,920 - resumatch-api - INFO - Starting background embedding generation for resume: resume_a5f08a24
2025-05-24 15:14:57,927 - resumatch-api - INFO - Background embedding generation completed for resume: resume_a5f08a24 in 0.01 seconds
2025-05-24 15:14:57,928 - resumatch-api - INFO - Processing job with ID: job_1748079897921_software_engineer
2025-05-24 15:14:57,930 - resumatch-api - INFO - Job embeddings generated for: job_1748079897921_software_engineer
2025-05-24 15:14:57,935 - resumatch-api - INFO - Matching resume resume_a5f08a24 with jobs (threshold=0.5, limit=5)
2025-05-24 15:14:57,935 - resumatch-api - INFO - Matching resume with 3 jobs
2025-05-24 15:14:57,972 - resumatch-api - INFO - Match completed in 0.04 seconds. Found 3 matches.
2025-05-24 15:15:15,652 - resumatch-api - INFO - Processing document upload: Gaurav Singh Resume 2.pdf
2025-05-24 15:15:15,652 - resumatch-api - INFO - Processing PDF file: Gaurav Singh Resume 2.pdf
2025-05-24 15:15:15,684 - resumatch-api - INFO - Text extraction complete. Extracted 3393 characters
2025-05-24 15:15:15,684 - resumatch-api - INFO - Extracting entities from resume text
2025-05-24 15:15:15,703 - resumatch-api - INFO - Extracted entities: {'name': '', 'email': '', 'phone': '8795347233', 'education': [{'institution': 'Indian Institute of Information Technology, Gwalior, Madhya Pradesh', 'degree': 'Integrated B.Tech and M.Tech in Information Technology', 'year': 'December 2021 - Expected May 2026'}, {'institution': 'Rani Laxmi Bai Memorial, Lucknow, Uttar Pradesh', 'degree': 'All India Senior School Certificate', 'year': 'March 2021'}], 'skills': ['Python', 'Go', 'Dart', 'SQL', 'Flutter', 'Android', 'Firebase', 'MySQL', 'MongoDB', 'Firebase Firestore', 'Firestore', 'AWS', 'Docker', 'Git', 'GitHub', 'TensorFlow', 'OAuth', 'S3', 'Google Sign-In', 'Riverpod', 'Play Store', 'Visual Studio Code', 'Android Studio', 'Visual Studio', 'Data Structures and Algorithms', 'Database Management Systems', 'Object-Oriented Programming', 'Operating Systems', 'Computer Networks', 'Accessibility', 'Authentication'], 'experience': [{'title': 'Flutter Development Intern', 'organization': 'Wormos Corporation', 'dates': 'December 2023 - February 2024'}], 'projects': [{'title': 'Exodus', 'description': 'Cross-platform app with adaptive streaming capabilities', 'technologies': 'Flutter Framework, Python, AWS', 'dates': 'Not specified'}], 'certifications': [], 'languages': [], 'summary': '', 'location': '', 'urls': [], 'dates': []}
2025-05-24 15:15:15,704 - resumatch-api - INFO - Generated resume ID: resume_3d9230a0
2025-05-24 15:15:15,704 - resumatch-api - INFO - Resume data stored in database with ID: resume_3d9230a0
2025-05-24 15:15:15,704 - resumatch-api - INFO - Scheduling background embedding generation for resume: resume_3d9230a0
2025-05-24 15:15:15,704 - resumatch-api - INFO - Resume processing completed in 0.05 seconds
2025-05-24 15:15:15,705 - resumatch-api - INFO - Starting background embedding generation for resume: resume_3d9230a0
2025-05-24 15:15:15,706 - resumatch-api - INFO - Background embedding generation completed for resume: resume_3d9230a0 in 0.00 seconds
2025-05-24 15:15:15,707 - resumatch-api - INFO - Processing job with ID: job_1748079915705_software_engineer
2025-05-24 15:15:15,708 - resumatch-api - INFO - Job embeddings generated for: job_1748079915705_software_engineer
2025-05-24 15:15:15,714 - resumatch-api - INFO - Matching resume resume_3d9230a0 with jobs (threshold=0.5, limit=5)
2025-05-24 15:15:15,714 - resumatch-api - INFO - Matching resume with 4 jobs
2025-05-24 15:15:15,716 - resumatch-api - INFO - Match completed in 0.00 seconds. Found 4 matches.
2025-05-24 15:38:39,756 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:38:39,757 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:38:39,757 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:38:39,757 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:38:39,757 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:38:39,771 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:42:18,765 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:42:18,765 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:42:18,765 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:42:18,765 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:42:18,765 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:42:18,782 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:44:24,187 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:44:24,187 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:44:24,187 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:44:24,187 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:44:24,187 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:44:24,200 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:44:29,778 - resumatch-api - WARNING - Failed to compute embedding for resume f1e9c14e-0f61-4801-ae19-b47cfe0d986b: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:44:29,778 - resumatch-api - INFO - Resume uploaded and processed: f1e9c14e-0f61-4801-ae19-b47cfe0d986b
2025-05-24 15:49:29,711 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:49:29,711 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:49:29,711 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:49:29,711 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:49:29,711 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:49:29,716 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:49:47,440 - resumatch-api - WARNING - Failed to compute embedding for resume 9820b78b-b9e6-4582-92c9-d28c5928b460: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:49:47,440 - resumatch-api - INFO - Resume uploaded and processed: 9820b78b-b9e6-4582-92c9-d28c5928b460
2025-05-24 15:51:58,453 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:51:58,453 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:51:58,453 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:51:58,453 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:51:58,453 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:51:58,458 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:52:02,085 - resumatch-api - WARNING - Failed to compute embedding for resume 09d49528-3b00-4ad0-aa49-133c93201748: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:52:02,085 - resumatch-api - INFO - Resume uploaded and processed: 09d49528-3b00-4ad0-aa49-133c93201748
2025-05-24 15:52:43,886 - resumatch-api - ERROR - Failed to recompute embedding for resume 09d49528-3b00-4ad0-aa49-133c93201748: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:53:46,041 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:53:46,041 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:53:46,041 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:53:46,041 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:53:46,041 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:53:46,045 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:53:52,912 - resumatch-api - WARNING - Failed to compute embedding for resume 54741361-9f2b-4546-bd30-213d1c3129da: 'TextEmbedder' object has no attribute 'get_embedding'
2025-05-24 15:53:52,913 - resumatch-api - INFO - Resume uploaded and processed: 54741361-9f2b-4546-bd30-213d1c3129da
2025-05-24 15:54:21,267 - resumatch-api - INFO - Recomputing embedding for resume 54741361-9f2b-4546-bd30-213d1c3129da. Extracted text length: 3440
2025-05-24 15:54:21,268 - resumatch-api - ERROR - Failed to recompute embedding for resume 54741361-9f2b-4546-bd30-213d1c3129da: 'TextEmbedder' object has no attribute 'get_embedding'
Extracted text: Gaurav Singh Email : gaurav91345gmail.com Mobile : 91 8795347233 LinkedIn: gaurav-singh Leetcode: gaurav91345 Github: gaurav-singh7092 Summary Software Developer with experience in developing user-cen...
2025-05-24 15:56:26,541 - resumatch-api - INFO - Initializing API components...
2025-05-24 15:56:26,541 - resumatch-api - INFO - Document processor initialized
2025-05-24 15:56:26,541 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 15:56:26,541 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 15:56:26,541 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 15:56:26,545 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 15:56:29,232 - resumatch-api - INFO - Resume uploaded and processed: d0a4d106-6d04-48a3-93af-9c051073048c
2025-05-24 16:05:03,318 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:05:03,318 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:05:03,318 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:05:03,318 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:05:03,318 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:05:03,336 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:05:30,227 - resumatch-api - INFO - Job embedding computed for job c4d2f768-4b6b-441c-aab9-cc9a8320421d
2025-05-24 16:05:30,227 - resumatch-api - INFO - Job processed and stored: c4d2f768-4b6b-441c-aab9-cc9a8320421d
2025-05-24 16:05:30,230 - resumatch-api - INFO - Job embedding computed for job 8302e0a7-279f-44de-8da0-eb16338b483c
2025-05-24 16:05:30,230 - resumatch-api - INFO - Job processed and stored: 8302e0a7-279f-44de-8da0-eb16338b483c
2025-05-24 16:05:30,232 - resumatch-api - INFO - Job embedding computed for job 541e6fd8-9279-4f8a-ba5f-42c267cf0f8b
2025-05-24 16:05:30,232 - resumatch-api - INFO - Job processed and stored: 541e6fd8-9279-4f8a-ba5f-42c267cf0f8b
2025-05-24 16:05:30,233 - resumatch-api - INFO - Job embedding computed for job c9ed3d46-b27c-48f8-8031-1ecd2f12853e
2025-05-24 16:05:30,233 - resumatch-api - INFO - Job processed and stored: c9ed3d46-b27c-48f8-8031-1ecd2f12853e
2025-05-24 16:05:30,235 - resumatch-api - INFO - Job embedding computed for job 65292eea-c3c2-4429-862c-7e05bb48a270
2025-05-24 16:05:30,235 - resumatch-api - INFO - Job processed and stored: 65292eea-c3c2-4429-862c-7e05bb48a270
2025-05-24 16:05:40,461 - resumatch-api - INFO - Job embedding computed for job f50c8088-b03d-4609-be57-69610a01b6cc
2025-05-24 16:05:40,462 - resumatch-api - INFO - Job processed and stored: f50c8088-b03d-4609-be57-69610a01b6cc
2025-05-24 16:05:40,463 - resumatch-api - INFO - Job embedding computed for job 74d8a6db-158e-4634-ada0-bf1a8bd0de23
2025-05-24 16:05:40,464 - resumatch-api - INFO - Job processed and stored: 74d8a6db-158e-4634-ada0-bf1a8bd0de23
2025-05-24 16:05:40,465 - resumatch-api - INFO - Job embedding computed for job c82fd35c-4e87-4572-946a-5ced543f687f
2025-05-24 16:05:40,465 - resumatch-api - INFO - Job processed and stored: c82fd35c-4e87-4572-946a-5ced543f687f
2025-05-24 16:05:40,466 - resumatch-api - INFO - Job embedding computed for job efb4eca8-9b03-431d-9e26-11c08051fd1a
2025-05-24 16:05:40,466 - resumatch-api - INFO - Job processed and stored: efb4eca8-9b03-431d-9e26-11c08051fd1a
2025-05-24 16:05:40,468 - resumatch-api - INFO - Job embedding computed for job 3ad60075-1d32-4355-b081-754dab8810b5
2025-05-24 16:05:40,468 - resumatch-api - INFO - Job processed and stored: 3ad60075-1d32-4355-b081-754dab8810b5
2025-05-24 16:06:18,339 - resumatch-api - INFO - Resume uploaded and processed: e6da14eb-c57e-4e12-a0a1-e7e3bfb58d58
2025-05-24 16:07:38,476 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:07:38,476 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:07:38,476 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:07:38,476 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:07:38,476 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:07:38,480 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:08:03,878 - resumatch-api - INFO - Job embedding computed for job 6064b296-ea37-44ba-ab9d-3eb694bc55ca
2025-05-24 16:08:03,878 - resumatch-api - INFO - Job processed and stored: 6064b296-ea37-44ba-ab9d-3eb694bc55ca
2025-05-24 16:08:03,881 - resumatch-api - INFO - Job embedding computed for job 27db8e68-87b7-4462-8db3-614652f5a5ca
2025-05-24 16:08:03,881 - resumatch-api - INFO - Job processed and stored: 27db8e68-87b7-4462-8db3-614652f5a5ca
2025-05-24 16:08:03,882 - resumatch-api - INFO - Job embedding computed for job 05b0b808-1752-490e-895c-a4aa0cd24a72
2025-05-24 16:08:03,882 - resumatch-api - INFO - Job processed and stored: 05b0b808-1752-490e-895c-a4aa0cd24a72
2025-05-24 16:08:03,884 - resumatch-api - INFO - Job embedding computed for job c7e09c59-ea39-4d72-bcd1-5ba255f4609c
2025-05-24 16:08:03,884 - resumatch-api - INFO - Job processed and stored: c7e09c59-ea39-4d72-bcd1-5ba255f4609c
2025-05-24 16:08:03,885 - resumatch-api - INFO - Job embedding computed for job 55239ec4-9301-4d90-962a-67c52292dfb2
2025-05-24 16:08:03,885 - resumatch-api - INFO - Job processed and stored: 55239ec4-9301-4d90-962a-67c52292dfb2
2025-05-24 16:08:10,156 - resumatch-api - INFO - Resume uploaded and processed: 980c1689-0f42-4467-8bbf-c2d56cf6e8f4
2025-05-24 16:09:08,154 - resumatch-api - INFO - Resume uploaded and processed: af5e9ac3-7a07-47df-a737-44e5949aee5a
2025-05-24 16:10:10,046 - resumatch-api - INFO - Resume uploaded and processed: c0d7a270-c155-4ebe-b348-7133b64c07e9
2025-05-24 16:18:30,785 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:18:30,785 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:18:30,785 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:18:30,785 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:18:30,785 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:18:30,791 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:18:55,945 - resumatch-api - INFO - Job embedding computed for job 2b6a37ca-6209-40d6-8767-b2c1441d102a
2025-05-24 16:18:55,945 - resumatch-api - INFO - Job processed and stored: 2b6a37ca-6209-40d6-8767-b2c1441d102a
2025-05-24 16:18:55,948 - resumatch-api - INFO - Job embedding computed for job 0c9f4eac-84e8-4ac9-820d-2c848e75b340
2025-05-24 16:18:55,948 - resumatch-api - INFO - Job processed and stored: 0c9f4eac-84e8-4ac9-820d-2c848e75b340
2025-05-24 16:18:55,950 - resumatch-api - INFO - Job embedding computed for job 317d9060-7762-47a4-be0b-4076520dca3b
2025-05-24 16:18:55,950 - resumatch-api - INFO - Job processed and stored: 317d9060-7762-47a4-be0b-4076520dca3b
2025-05-24 16:18:55,953 - resumatch-api - INFO - Job embedding computed for job b603512b-00da-4691-9dea-771513c51369
2025-05-24 16:18:55,953 - resumatch-api - INFO - Job processed and stored: b603512b-00da-4691-9dea-771513c51369
2025-05-24 16:18:55,954 - resumatch-api - INFO - Job embedding computed for job 32e9c5e1-a615-4e48-bf77-baa7d66f367b
2025-05-24 16:18:55,954 - resumatch-api - INFO - Job processed and stored: 32e9c5e1-a615-4e48-bf77-baa7d66f367b
2025-05-24 16:19:37,764 - resumatch-api - INFO - Extracted entities for resume 065b022a-a19c-41e1-856b-96c354a9dc1b
2025-05-24 16:19:37,765 - resumatch-api - INFO - Resume uploaded and processed: 065b022a-a19c-41e1-856b-96c354a9dc1b
2025-05-24 16:20:38,654 - resumatch-api - INFO - Extracted entities for resume 99ab731c-de6d-4427-a1a9-1eec2e132b5f
2025-05-24 16:20:38,657 - resumatch-api - INFO - Resume uploaded and processed: 99ab731c-de6d-4427-a1a9-1eec2e132b5f
2025-05-24 16:21:19,732 - resumatch-api - INFO - Extracted entities for resume bf32021c-7f11-4471-b512-9836aa5aef6f
2025-05-24 16:21:19,734 - resumatch-api - INFO - Resume uploaded and processed: bf32021c-7f11-4471-b512-9836aa5aef6f
2025-05-24 16:27:42,398 - resumatch-api - INFO - Job embedding computed for job 1846aeef-6fec-4b0e-9b6f-16c7181b2748
2025-05-24 16:27:42,399 - resumatch-api - INFO - Job processed and stored: 1846aeef-6fec-4b0e-9b6f-16c7181b2748
2025-05-24 16:27:42,402 - resumatch-api - INFO - Job embedding computed for job 99cb962f-fa7d-4929-bf0f-999eb1f28784
2025-05-24 16:27:42,402 - resumatch-api - INFO - Job processed and stored: 99cb962f-fa7d-4929-bf0f-999eb1f28784
2025-05-24 16:27:42,403 - resumatch-api - INFO - Job embedding computed for job 741ac0ff-85fd-44c9-abb6-882ad64fb5c4
2025-05-24 16:27:42,403 - resumatch-api - INFO - Job processed and stored: 741ac0ff-85fd-44c9-abb6-882ad64fb5c4
2025-05-24 16:27:42,407 - resumatch-api - INFO - Job embedding computed for job b393513d-4374-4d9b-9d8d-8c976b8af017
2025-05-24 16:27:42,407 - resumatch-api - INFO - Job processed and stored: b393513d-4374-4d9b-9d8d-8c976b8af017
2025-05-24 16:27:42,409 - resumatch-api - INFO - Job embedding computed for job 64b7519b-48cc-4fe9-8eb2-b22648a5e45d
2025-05-24 16:27:42,409 - resumatch-api - INFO - Job processed and stored: 64b7519b-48cc-4fe9-8eb2-b22648a5e45d
2025-05-24 16:27:42,411 - resumatch-api - INFO - Job embedding computed for job a58f422c-3f92-4ba2-96d8-14e23a4e6c41
2025-05-24 16:27:42,411 - resumatch-api - INFO - Job processed and stored: a58f422c-3f92-4ba2-96d8-14e23a4e6c41
2025-05-24 16:27:42,413 - resumatch-api - INFO - Job embedding computed for job 23a43865-2263-443a-ad4d-79af62f9bdcc
2025-05-24 16:27:42,413 - resumatch-api - INFO - Job processed and stored: 23a43865-2263-443a-ad4d-79af62f9bdcc
2025-05-24 16:27:42,416 - resumatch-api - INFO - Job embedding computed for job 76e1724b-13cc-4ff1-b35e-24c2afb2ef31
2025-05-24 16:27:42,416 - resumatch-api - INFO - Job processed and stored: 76e1724b-13cc-4ff1-b35e-24c2afb2ef31
2025-05-24 16:27:42,417 - resumatch-api - INFO - Job embedding computed for job cdd1a9c7-d5c5-4a5c-b591-c61127195122
2025-05-24 16:27:42,418 - resumatch-api - INFO - Job processed and stored: cdd1a9c7-d5c5-4a5c-b591-c61127195122
2025-05-24 16:27:42,419 - resumatch-api - INFO - Job embedding computed for job 96dec574-5ea8-43ac-9665-817be43849d4
2025-05-24 16:27:42,419 - resumatch-api - INFO - Job processed and stored: 96dec574-5ea8-43ac-9665-817be43849d4
2025-05-24 16:27:42,421 - resumatch-api - INFO - Job embedding computed for job ad63d35e-9b66-4dc7-8ab1-05d9ef85757b
2025-05-24 16:27:42,421 - resumatch-api - INFO - Job processed and stored: ad63d35e-9b66-4dc7-8ab1-05d9ef85757b
2025-05-24 16:27:42,424 - resumatch-api - INFO - Job embedding computed for job 06f1cd30-8740-4d11-9d05-bcc19c7cb0d8
2025-05-24 16:27:42,424 - resumatch-api - INFO - Job processed and stored: 06f1cd30-8740-4d11-9d05-bcc19c7cb0d8
2025-05-24 16:27:42,426 - resumatch-api - INFO - Job embedding computed for job acce5cdc-30c3-4153-b607-683f61960609
2025-05-24 16:27:42,426 - resumatch-api - INFO - Job processed and stored: acce5cdc-30c3-4153-b607-683f61960609
2025-05-24 16:27:42,428 - resumatch-api - INFO - Job embedding computed for job 1a614b05-e0a8-4fda-835b-d60d63c22654
2025-05-24 16:27:42,428 - resumatch-api - INFO - Job processed and stored: 1a614b05-e0a8-4fda-835b-d60d63c22654
2025-05-24 16:27:42,430 - resumatch-api - INFO - Job embedding computed for job dceadbe3-468b-4458-9ef5-4698154a1463
2025-05-24 16:27:42,430 - resumatch-api - INFO - Job processed and stored: dceadbe3-468b-4458-9ef5-4698154a1463
2025-05-24 16:27:42,431 - resumatch-api - INFO - Job embedding computed for job 782481fa-9209-4c55-b30c-0e02ef8632d4
2025-05-24 16:27:42,431 - resumatch-api - INFO - Job processed and stored: 782481fa-9209-4c55-b30c-0e02ef8632d4
2025-05-24 16:27:42,433 - resumatch-api - INFO - Job embedding computed for job 53f87cb9-27c8-456e-a8f6-41cd249b8163
2025-05-24 16:27:42,433 - resumatch-api - INFO - Job processed and stored: 53f87cb9-27c8-456e-a8f6-41cd249b8163
2025-05-24 16:28:39,260 - resumatch-api - INFO - Extracted entities for resume 21ae735f-37ac-4119-bf7d-596c6dc66f6e
2025-05-24 16:28:39,261 - resumatch-api - INFO - Resume uploaded and processed: 21ae735f-37ac-4119-bf7d-596c6dc66f6e
2025-05-24 16:30:58,994 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:30:58,994 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:30:58,994 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:30:58,994 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:30:58,994 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:30:59,006 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:31:25,409 - resumatch-api - INFO - Job embedding computed for job 3a7af98b-35da-4759-84fc-59c3859f9dd7
2025-05-24 16:31:25,410 - resumatch-api - INFO - Job processed and stored: 3a7af98b-35da-4759-84fc-59c3859f9dd7
2025-05-24 16:31:25,412 - resumatch-api - INFO - Job embedding computed for job 6fd5bafd-83dd-41f5-8b87-e007bcaa67ed
2025-05-24 16:31:25,412 - resumatch-api - INFO - Job processed and stored: 6fd5bafd-83dd-41f5-8b87-e007bcaa67ed
2025-05-24 16:31:25,414 - resumatch-api - INFO - Job embedding computed for job 12e0b888-f329-4eb4-b2d1-aada5c870d8a
2025-05-24 16:31:25,414 - resumatch-api - INFO - Job processed and stored: 12e0b888-f329-4eb4-b2d1-aada5c870d8a
2025-05-24 16:31:25,415 - resumatch-api - INFO - Job embedding computed for job b7a8d576-73a9-4a9b-8212-e18459524346
2025-05-24 16:31:25,415 - resumatch-api - INFO - Job processed and stored: b7a8d576-73a9-4a9b-8212-e18459524346
2025-05-24 16:31:25,417 - resumatch-api - INFO - Job embedding computed for job b0413529-59bb-4257-88e4-fccb17a60ed5
2025-05-24 16:31:25,417 - resumatch-api - INFO - Job processed and stored: b0413529-59bb-4257-88e4-fccb17a60ed5
2025-05-24 16:31:25,418 - resumatch-api - INFO - Job embedding computed for job bfd8832c-6051-4800-8675-3fa7e837b116
2025-05-24 16:31:25,418 - resumatch-api - INFO - Job processed and stored: bfd8832c-6051-4800-8675-3fa7e837b116
2025-05-24 16:31:25,420 - resumatch-api - INFO - Job embedding computed for job 4aeda728-21d2-4b4d-b494-93bc9f8feb03
2025-05-24 16:31:25,420 - resumatch-api - INFO - Job processed and stored: 4aeda728-21d2-4b4d-b494-93bc9f8feb03
2025-05-24 16:31:25,423 - resumatch-api - INFO - Job embedding computed for job a2571642-fa09-41d3-abdd-19cdca77bc3c
2025-05-24 16:31:25,423 - resumatch-api - INFO - Job processed and stored: a2571642-fa09-41d3-abdd-19cdca77bc3c
2025-05-24 16:31:25,425 - resumatch-api - INFO - Job embedding computed for job 82cd3813-f28e-4299-97ec-4d662425d603
2025-05-24 16:31:25,425 - resumatch-api - INFO - Job processed and stored: 82cd3813-f28e-4299-97ec-4d662425d603
2025-05-24 16:31:25,426 - resumatch-api - INFO - Job embedding computed for job 14abc96f-4a90-41cd-9c01-e4c3c8f5e387
2025-05-24 16:31:25,426 - resumatch-api - INFO - Job processed and stored: 14abc96f-4a90-41cd-9c01-e4c3c8f5e387
2025-05-24 16:31:25,428 - resumatch-api - INFO - Job embedding computed for job 9352fcbb-a26d-4efe-bebf-7dfb0d273624
2025-05-24 16:31:25,428 - resumatch-api - INFO - Job processed and stored: 9352fcbb-a26d-4efe-bebf-7dfb0d273624
2025-05-24 16:31:25,429 - resumatch-api - INFO - Job embedding computed for job bda7c9f7-1f8c-4506-afa6-e4b813edfc8b
2025-05-24 16:31:25,429 - resumatch-api - INFO - Job processed and stored: bda7c9f7-1f8c-4506-afa6-e4b813edfc8b
2025-05-24 16:31:25,431 - resumatch-api - INFO - Job embedding computed for job d5d5b302-59a1-47a0-a779-e8a8d1fd893a
2025-05-24 16:31:25,431 - resumatch-api - INFO - Job processed and stored: d5d5b302-59a1-47a0-a779-e8a8d1fd893a
2025-05-24 16:31:25,433 - resumatch-api - INFO - Job embedding computed for job 19f3d478-46db-4804-82c3-045f0a452f59
2025-05-24 16:31:25,433 - resumatch-api - INFO - Job processed and stored: 19f3d478-46db-4804-82c3-045f0a452f59
2025-05-24 16:31:25,434 - resumatch-api - INFO - Job embedding computed for job f75b0497-99c8-4f5a-ac47-9bf22d569f0e
2025-05-24 16:31:25,434 - resumatch-api - INFO - Job processed and stored: f75b0497-99c8-4f5a-ac47-9bf22d569f0e
2025-05-24 16:31:25,436 - resumatch-api - INFO - Job embedding computed for job 008b14fa-40fb-426d-9eb9-cf6cc23630cc
2025-05-24 16:31:25,436 - resumatch-api - INFO - Job processed and stored: 008b14fa-40fb-426d-9eb9-cf6cc23630cc
2025-05-24 16:31:25,457 - resumatch-api - INFO - Job embedding computed for job 5ca8b255-d059-42dc-ab71-f0a5452fbbb1
2025-05-24 16:31:25,457 - resumatch-api - INFO - Job processed and stored: 5ca8b255-d059-42dc-ab71-f0a5452fbbb1
2025-05-24 16:31:32,284 - resumatch-api - INFO - Extracted entities for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6
2025-05-24 16:31:32,285 - resumatch-api - INFO - Resume uploaded and processed: ad89397b-c746-4abf-beeb-6a78cb0c23e6
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 3a7af98b-35da-4759-84fc-59c3859f9dd7: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 6fd5bafd-83dd-41f5-8b87-e007bcaa67ed: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 12e0b888-f329-4eb4-b2d1-aada5c870d8a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job b7a8d576-73a9-4a9b-8212-e18459524346: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job b0413529-59bb-4257-88e4-fccb17a60ed5: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job bfd8832c-6051-4800-8675-3fa7e837b116: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 4aeda728-21d2-4b4d-b494-93bc9f8feb03: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,306 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job a2571642-fa09-41d3-abdd-19cdca77bc3c: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 82cd3813-f28e-4299-97ec-4d662425d603: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 14abc96f-4a90-41cd-9c01-e4c3c8f5e387: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 9352fcbb-a26d-4efe-bebf-7dfb0d273624: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job bda7c9f7-1f8c-4506-afa6-e4b813edfc8b: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job d5d5b302-59a1-47a0-a779-e8a8d1fd893a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 19f3d478-46db-4804-82c3-045f0a452f59: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job f75b0497-99c8-4f5a-ac47-9bf22d569f0e: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,307 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 008b14fa-40fb-426d-9eb9-cf6cc23630cc: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,308 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 5ca8b255-d059-42dc-ab71-f0a5452fbbb1: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,315 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 3a7af98b-35da-4759-84fc-59c3859f9dd7: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,315 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 6fd5bafd-83dd-41f5-8b87-e007bcaa67ed: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 12e0b888-f329-4eb4-b2d1-aada5c870d8a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job b7a8d576-73a9-4a9b-8212-e18459524346: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job b0413529-59bb-4257-88e4-fccb17a60ed5: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job bfd8832c-6051-4800-8675-3fa7e837b116: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 4aeda728-21d2-4b4d-b494-93bc9f8feb03: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job a2571642-fa09-41d3-abdd-19cdca77bc3c: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 82cd3813-f28e-4299-97ec-4d662425d603: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 14abc96f-4a90-41cd-9c01-e4c3c8f5e387: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,316 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 9352fcbb-a26d-4efe-bebf-7dfb0d273624: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,317 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job bda7c9f7-1f8c-4506-afa6-e4b813edfc8b: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,317 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job d5d5b302-59a1-47a0-a779-e8a8d1fd893a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,317 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 19f3d478-46db-4804-82c3-045f0a452f59: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,317 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job f75b0497-99c8-4f5a-ac47-9bf22d569f0e: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,317 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 008b14fa-40fb-426d-9eb9-cf6cc23630cc: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,317 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 5ca8b255-d059-42dc-ab71-f0a5452fbbb1: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 3a7af98b-35da-4759-84fc-59c3859f9dd7: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 6fd5bafd-83dd-41f5-8b87-e007bcaa67ed: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 12e0b888-f329-4eb4-b2d1-aada5c870d8a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job b7a8d576-73a9-4a9b-8212-e18459524346: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job b0413529-59bb-4257-88e4-fccb17a60ed5: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job bfd8832c-6051-4800-8675-3fa7e837b116: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,326 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 4aeda728-21d2-4b4d-b494-93bc9f8feb03: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,327 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job a2571642-fa09-41d3-abdd-19cdca77bc3c: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,327 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 82cd3813-f28e-4299-97ec-4d662425d603: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,327 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 14abc96f-4a90-41cd-9c01-e4c3c8f5e387: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,327 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 9352fcbb-a26d-4efe-bebf-7dfb0d273624: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,328 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job bda7c9f7-1f8c-4506-afa6-e4b813edfc8b: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,328 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job d5d5b302-59a1-47a0-a779-e8a8d1fd893a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,328 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 19f3d478-46db-4804-82c3-045f0a452f59: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,328 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job f75b0497-99c8-4f5a-ac47-9bf22d569f0e: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,328 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 008b14fa-40fb-426d-9eb9-cf6cc23630cc: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:34,328 - resumatch-api - WARNING - Failed to compute similarity for resume ad89397b-c746-4abf-beeb-6a78cb0c23e6 and job 5ca8b255-d059-42dc-ab71-f0a5452fbbb1: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,773 - resumatch-api - INFO - Extracted entities for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f
2025-05-24 16:31:56,775 - resumatch-api - INFO - Resume uploaded and processed: 3a3ed1c9-4671-4700-af2a-96319875ba4f
2025-05-24 16:31:56,777 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 3a7af98b-35da-4759-84fc-59c3859f9dd7: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 6fd5bafd-83dd-41f5-8b87-e007bcaa67ed: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 12e0b888-f329-4eb4-b2d1-aada5c870d8a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job b7a8d576-73a9-4a9b-8212-e18459524346: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job b0413529-59bb-4257-88e4-fccb17a60ed5: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job bfd8832c-6051-4800-8675-3fa7e837b116: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 4aeda728-21d2-4b4d-b494-93bc9f8feb03: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job a2571642-fa09-41d3-abdd-19cdca77bc3c: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 82cd3813-f28e-4299-97ec-4d662425d603: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 14abc96f-4a90-41cd-9c01-e4c3c8f5e387: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 9352fcbb-a26d-4efe-bebf-7dfb0d273624: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job bda7c9f7-1f8c-4506-afa6-e4b813edfc8b: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job d5d5b302-59a1-47a0-a779-e8a8d1fd893a: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 19f3d478-46db-4804-82c3-045f0a452f59: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job f75b0497-99c8-4f5a-ac47-9bf22d569f0e: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 008b14fa-40fb-426d-9eb9-cf6cc23630cc: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:31:56,778 - resumatch-api - WARNING - Failed to compute similarity for resume 3a3ed1c9-4671-4700-af2a-96319875ba4f and job 5ca8b255-d059-42dc-ab71-f0a5452fbbb1: LegacyResumeMatcher.get_similarity() got an unexpected keyword argument 'resume_text'
2025-05-24 16:34:06,690 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:34:06,690 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:34:06,690 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:34:06,690 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:34:06,690 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:34:06,709 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:34:32,272 - resumatch-api - INFO - Job embedding computed for job fc822858-5368-4923-afca-ef2bbebf2c61
2025-05-24 16:34:32,273 - resumatch-api - INFO - Job processed and stored: fc822858-5368-4923-afca-ef2bbebf2c61
2025-05-24 16:34:32,275 - resumatch-api - INFO - Job embedding computed for job abbd9f32-8207-464b-89f4-e7d40aa6464d
2025-05-24 16:34:32,275 - resumatch-api - INFO - Job processed and stored: abbd9f32-8207-464b-89f4-e7d40aa6464d
2025-05-24 16:34:32,277 - resumatch-api - INFO - Job embedding computed for job aab113e3-22f5-49b5-af65-fc51e8bbea1d
2025-05-24 16:34:32,277 - resumatch-api - INFO - Job processed and stored: aab113e3-22f5-49b5-af65-fc51e8bbea1d
2025-05-24 16:34:32,278 - resumatch-api - INFO - Job embedding computed for job 1f46feb8-b6d5-4fa6-87d4-1adec2a3a14f
2025-05-24 16:34:32,278 - resumatch-api - INFO - Job processed and stored: 1f46feb8-b6d5-4fa6-87d4-1adec2a3a14f
2025-05-24 16:34:32,280 - resumatch-api - INFO - Job embedding computed for job e638f3ea-9e9c-4c87-9ddc-602c9cc2e8a2
2025-05-24 16:34:32,280 - resumatch-api - INFO - Job processed and stored: e638f3ea-9e9c-4c87-9ddc-602c9cc2e8a2
2025-05-24 16:34:32,282 - resumatch-api - INFO - Job embedding computed for job 528327a9-a7f6-492b-a304-01db9c126a0c
2025-05-24 16:34:32,282 - resumatch-api - INFO - Job processed and stored: 528327a9-a7f6-492b-a304-01db9c126a0c
2025-05-24 16:34:32,283 - resumatch-api - INFO - Job embedding computed for job aba1139e-b395-47f9-bdd2-7545afd5d0bf
2025-05-24 16:34:32,283 - resumatch-api - INFO - Job processed and stored: aba1139e-b395-47f9-bdd2-7545afd5d0bf
2025-05-24 16:34:32,285 - resumatch-api - INFO - Job embedding computed for job bf660f52-2eaf-4403-821e-35b1f7e2bf5d
2025-05-24 16:34:32,285 - resumatch-api - INFO - Job processed and stored: bf660f52-2eaf-4403-821e-35b1f7e2bf5d
2025-05-24 16:34:32,289 - resumatch-api - INFO - Job embedding computed for job 02d10441-dcdb-44c7-ba6e-4680410bdbe5
2025-05-24 16:34:32,289 - resumatch-api - INFO - Job processed and stored: 02d10441-dcdb-44c7-ba6e-4680410bdbe5
2025-05-24 16:34:32,292 - resumatch-api - INFO - Job embedding computed for job 4a6c8263-df72-402b-b10e-6ae697a8568c
2025-05-24 16:34:32,292 - resumatch-api - INFO - Job processed and stored: 4a6c8263-df72-402b-b10e-6ae697a8568c
2025-05-24 16:34:32,293 - resumatch-api - INFO - Job embedding computed for job 88d78fb0-a047-451c-9927-b90b24ce3445
2025-05-24 16:34:32,294 - resumatch-api - INFO - Job processed and stored: 88d78fb0-a047-451c-9927-b90b24ce3445
2025-05-24 16:34:32,295 - resumatch-api - INFO - Job embedding computed for job 098ebc9d-3c0a-46a7-a6a2-c64a73457972
2025-05-24 16:34:32,296 - resumatch-api - INFO - Job processed and stored: 098ebc9d-3c0a-46a7-a6a2-c64a73457972
2025-05-24 16:34:32,297 - resumatch-api - INFO - Job embedding computed for job f8d8dc79-400a-4c0c-b46c-cb726e5032b6
2025-05-24 16:34:32,297 - resumatch-api - INFO - Job processed and stored: f8d8dc79-400a-4c0c-b46c-cb726e5032b6
2025-05-24 16:34:32,299 - resumatch-api - INFO - Job embedding computed for job 6b303451-34d6-4703-8490-f90cff62a21b
2025-05-24 16:34:32,299 - resumatch-api - INFO - Job processed and stored: 6b303451-34d6-4703-8490-f90cff62a21b
2025-05-24 16:34:32,301 - resumatch-api - INFO - Job embedding computed for job d653ccf9-d3ea-40dc-87c2-70f46f82fa5a
2025-05-24 16:34:32,301 - resumatch-api - INFO - Job processed and stored: d653ccf9-d3ea-40dc-87c2-70f46f82fa5a
2025-05-24 16:34:32,302 - resumatch-api - INFO - Job embedding computed for job 309506ba-ef37-4c92-a00e-bf6635989dfd
2025-05-24 16:34:32,302 - resumatch-api - INFO - Job processed and stored: 309506ba-ef37-4c92-a00e-bf6635989dfd
2025-05-24 16:34:32,305 - resumatch-api - INFO - Job embedding computed for job 926910c3-707e-456c-b8f1-27e3ac765124
2025-05-24 16:34:32,305 - resumatch-api - INFO - Job processed and stored: 926910c3-707e-456c-b8f1-27e3ac765124
2025-05-24 16:34:39,023 - resumatch-api - INFO - Extracted entities for resume 8a058c39-a6ef-43a7-b69f-c2b4f36e4623
2025-05-24 16:34:39,024 - resumatch-api - INFO - Resume uploaded and processed: 8a058c39-a6ef-43a7-b69f-c2b4f36e4623
2025-05-24 16:34:44,717 - resumatch-api - INFO - Extracted entities for resume 571591ab-b7d8-460d-807e-3847583c3cb7
2025-05-24 16:34:44,718 - resumatch-api - INFO - Resume uploaded and processed: 571591ab-b7d8-460d-807e-3847583c3cb7
2025-05-24 16:36:12,914 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:36:12,914 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:36:12,914 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:36:12,914 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:36:12,914 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:36:12,927 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:36:50,981 - resumatch-api - INFO - Extracted entities for resume 2db3e847-86e1-4df3-9dd3-716c27eea975
2025-05-24 16:36:50,982 - resumatch-api - INFO - Resume uploaded and processed: 2db3e847-86e1-4df3-9dd3-716c27eea975
2025-05-24 16:37:04,032 - resumatch-api - INFO - Job embedding computed for job fd76a80c-f466-4f92-9f57-5e826c581fef
2025-05-24 16:37:04,032 - resumatch-api - INFO - Job processed and stored: fd76a80c-f466-4f92-9f57-5e826c581fef
2025-05-24 16:37:04,034 - resumatch-api - INFO - Job embedding computed for job d26ec009-e768-4e2e-bfd9-f4fa570194b5
2025-05-24 16:37:04,034 - resumatch-api - INFO - Job processed and stored: d26ec009-e768-4e2e-bfd9-f4fa570194b5
2025-05-24 16:37:04,036 - resumatch-api - INFO - Job embedding computed for job ceffbc10-76eb-4b01-a745-47294eef7e37
2025-05-24 16:37:04,036 - resumatch-api - INFO - Job processed and stored: ceffbc10-76eb-4b01-a745-47294eef7e37
2025-05-24 16:37:04,037 - resumatch-api - INFO - Job embedding computed for job de64715c-af69-46eb-b1a8-8b8f86a7ac5c
2025-05-24 16:37:04,037 - resumatch-api - INFO - Job processed and stored: de64715c-af69-46eb-b1a8-8b8f86a7ac5c
2025-05-24 16:37:04,040 - resumatch-api - INFO - Job embedding computed for job 0034f68d-d9d0-489d-8658-1bff66e4c1c4
2025-05-24 16:37:04,040 - resumatch-api - INFO - Job processed and stored: 0034f68d-d9d0-489d-8658-1bff66e4c1c4
2025-05-24 16:37:04,043 - resumatch-api - INFO - Job embedding computed for job 4ba955f2-7ee7-4faf-96fd-09e467d3ab3f
2025-05-24 16:37:04,043 - resumatch-api - INFO - Job processed and stored: 4ba955f2-7ee7-4faf-96fd-09e467d3ab3f
2025-05-24 16:37:04,045 - resumatch-api - INFO - Job embedding computed for job 836c8ee8-2b73-4b41-83a2-6bb24c3edf5c
2025-05-24 16:37:04,045 - resumatch-api - INFO - Job processed and stored: 836c8ee8-2b73-4b41-83a2-6bb24c3edf5c
2025-05-24 16:37:04,047 - resumatch-api - INFO - Job embedding computed for job 73fb4e16-77df-4e2b-a806-9feb2c8108c5
2025-05-24 16:37:04,047 - resumatch-api - INFO - Job processed and stored: 73fb4e16-77df-4e2b-a806-9feb2c8108c5
2025-05-24 16:37:04,049 - resumatch-api - INFO - Job embedding computed for job c623ff0c-d307-4c51-a7f1-d83f4f1bfafd
2025-05-24 16:37:04,049 - resumatch-api - INFO - Job processed and stored: c623ff0c-d307-4c51-a7f1-d83f4f1bfafd
2025-05-24 16:37:04,050 - resumatch-api - INFO - Job embedding computed for job a908b9c0-8f04-4422-8852-e0b350778ce8
2025-05-24 16:37:04,050 - resumatch-api - INFO - Job processed and stored: a908b9c0-8f04-4422-8852-e0b350778ce8
2025-05-24 16:37:04,052 - resumatch-api - INFO - Job embedding computed for job 8e39ab3b-439e-4f27-815b-6f9f3a7d646b
2025-05-24 16:37:04,052 - resumatch-api - INFO - Job processed and stored: 8e39ab3b-439e-4f27-815b-6f9f3a7d646b
2025-05-24 16:37:04,054 - resumatch-api - INFO - Job embedding computed for job 2a14cd16-2b99-43fe-8c0e-44f6b8173548
2025-05-24 16:37:04,054 - resumatch-api - INFO - Job processed and stored: 2a14cd16-2b99-43fe-8c0e-44f6b8173548
2025-05-24 16:37:04,057 - resumatch-api - INFO - Job embedding computed for job ae85968c-5e24-4db7-b79c-18d28347f634
2025-05-24 16:37:04,057 - resumatch-api - INFO - Job processed and stored: ae85968c-5e24-4db7-b79c-18d28347f634
2025-05-24 16:37:04,059 - resumatch-api - INFO - Job embedding computed for job 53d0e699-9fbc-4cd1-ae7c-c55e97128a02
2025-05-24 16:37:04,059 - resumatch-api - INFO - Job processed and stored: 53d0e699-9fbc-4cd1-ae7c-c55e97128a02
2025-05-24 16:37:04,061 - resumatch-api - INFO - Job embedding computed for job 478e7dcb-c10b-4b8a-a3e5-122ee763750b
2025-05-24 16:37:04,061 - resumatch-api - INFO - Job processed and stored: 478e7dcb-c10b-4b8a-a3e5-122ee763750b
2025-05-24 16:37:04,063 - resumatch-api - INFO - Job embedding computed for job 1497d09f-bf16-496c-ba3e-f308ba2d198e
2025-05-24 16:37:04,063 - resumatch-api - INFO - Job processed and stored: 1497d09f-bf16-496c-ba3e-f308ba2d198e
2025-05-24 16:37:04,065 - resumatch-api - INFO - Job embedding computed for job c62afccc-f29a-459b-a1a2-29e5551e6152
2025-05-24 16:37:04,065 - resumatch-api - INFO - Job processed and stored: c62afccc-f29a-459b-a1a2-29e5551e6152
2025-05-24 16:37:09,738 - resumatch-api - INFO - Extracted entities for resume 76f166f1-7306-4732-8a68-3ecb230c03a9
2025-05-24 16:37:09,739 - resumatch-api - INFO - Resume uploaded and processed: 76f166f1-7306-4732-8a68-3ecb230c03a9
2025-05-24 16:37:17,853 - resumatch-api - INFO - Extracted entities for resume f43706b6-6453-40be-8fd7-46038598e290
2025-05-24 16:37:17,855 - resumatch-api - INFO - Resume uploaded and processed: f43706b6-6453-40be-8fd7-46038598e290
2025-05-24 16:39:04,223 - resumatch-api - INFO - Extracted entities for resume 4b6837a1-cd67-4839-b107-f453f49f7cd0
2025-05-24 16:39:04,224 - resumatch-api - INFO - Resume uploaded and processed: 4b6837a1-cd67-4839-b107-f453f49f7cd0
2025-05-24 16:41:42,530 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:41:42,530 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:41:42,530 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:41:42,530 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:41:42,530 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:41:42,553 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:42:08,435 - resumatch-api - INFO - Job embedding computed for job e579f84d-ac3a-4a95-a244-973dc7f6c838
2025-05-24 16:42:08,435 - resumatch-api - INFO - Job processed and stored: e579f84d-ac3a-4a95-a244-973dc7f6c838
2025-05-24 16:42:08,438 - resumatch-api - INFO - Job embedding computed for job cfa4da8e-ebef-4142-868e-0715104210e1
2025-05-24 16:42:08,438 - resumatch-api - INFO - Job processed and stored: cfa4da8e-ebef-4142-868e-0715104210e1
2025-05-24 16:42:08,440 - resumatch-api - INFO - Job embedding computed for job 13dcdef1-9a36-4c6f-a37a-7ab48e713a0b
2025-05-24 16:42:08,440 - resumatch-api - INFO - Job processed and stored: 13dcdef1-9a36-4c6f-a37a-7ab48e713a0b
2025-05-24 16:42:08,441 - resumatch-api - INFO - Job embedding computed for job 8e17467c-7f03-4bb2-bc72-9bae3898d8f0
2025-05-24 16:42:08,441 - resumatch-api - INFO - Job processed and stored: 8e17467c-7f03-4bb2-bc72-9bae3898d8f0
2025-05-24 16:42:08,443 - resumatch-api - INFO - Job embedding computed for job d83a957e-5cc8-4715-9442-96ccb753b664
2025-05-24 16:42:08,443 - resumatch-api - INFO - Job processed and stored: d83a957e-5cc8-4715-9442-96ccb753b664
2025-05-24 16:42:08,444 - resumatch-api - INFO - Job embedding computed for job 35ebf259-aa09-4348-955a-d28cc14c2526
2025-05-24 16:42:08,444 - resumatch-api - INFO - Job processed and stored: 35ebf259-aa09-4348-955a-d28cc14c2526
2025-05-24 16:42:08,446 - resumatch-api - INFO - Job embedding computed for job 252fc23f-ad94-408c-b2ed-0bcd5b3f5008
2025-05-24 16:42:08,446 - resumatch-api - INFO - Job processed and stored: 252fc23f-ad94-408c-b2ed-0bcd5b3f5008
2025-05-24 16:42:08,447 - resumatch-api - INFO - Job embedding computed for job d802fd88-3ec4-44d4-81f6-31069ee4368e
2025-05-24 16:42:08,447 - resumatch-api - INFO - Job processed and stored: d802fd88-3ec4-44d4-81f6-31069ee4368e
2025-05-24 16:42:08,449 - resumatch-api - INFO - Job embedding computed for job ed385cbf-95a3-4996-bf4d-11335ae6fa75
2025-05-24 16:42:08,449 - resumatch-api - INFO - Job processed and stored: ed385cbf-95a3-4996-bf4d-11335ae6fa75
2025-05-24 16:42:08,450 - resumatch-api - INFO - Job embedding computed for job 4cd6e5ed-946e-4460-9afd-e5d8f3623529
2025-05-24 16:42:08,451 - resumatch-api - INFO - Job processed and stored: 4cd6e5ed-946e-4460-9afd-e5d8f3623529
2025-05-24 16:42:08,452 - resumatch-api - INFO - Job embedding computed for job 4a43742e-6e9b-4019-9794-2ee8135edf16
2025-05-24 16:42:08,452 - resumatch-api - INFO - Job processed and stored: 4a43742e-6e9b-4019-9794-2ee8135edf16
2025-05-24 16:42:08,454 - resumatch-api - INFO - Job embedding computed for job eae67f44-792e-4c8d-bfa6-b2f563116d0a
2025-05-24 16:42:08,454 - resumatch-api - INFO - Job processed and stored: eae67f44-792e-4c8d-bfa6-b2f563116d0a
2025-05-24 16:42:08,456 - resumatch-api - INFO - Job embedding computed for job 9e8577bf-310a-4e56-9e84-2fb1978ce016
2025-05-24 16:42:08,456 - resumatch-api - INFO - Job processed and stored: 9e8577bf-310a-4e56-9e84-2fb1978ce016
2025-05-24 16:42:08,457 - resumatch-api - INFO - Job embedding computed for job 821e0070-edbc-4373-b884-ba2a33916541
2025-05-24 16:42:08,457 - resumatch-api - INFO - Job processed and stored: 821e0070-edbc-4373-b884-ba2a33916541
2025-05-24 16:42:08,459 - resumatch-api - INFO - Job embedding computed for job 77732cfa-ee8f-42d0-9cf5-4f7b0f066e65
2025-05-24 16:42:08,459 - resumatch-api - INFO - Job processed and stored: 77732cfa-ee8f-42d0-9cf5-4f7b0f066e65
2025-05-24 16:42:08,461 - resumatch-api - INFO - Job embedding computed for job d4c996a5-cc50-44aa-bf91-4a3ca558d84e
2025-05-24 16:42:08,461 - resumatch-api - INFO - Job processed and stored: d4c996a5-cc50-44aa-bf91-4a3ca558d84e
2025-05-24 16:42:08,462 - resumatch-api - INFO - Job embedding computed for job 0b7e6f62-f9f2-4cae-8ae9-47c20b7eeb7a
2025-05-24 16:42:08,463 - resumatch-api - INFO - Job processed and stored: 0b7e6f62-f9f2-4cae-8ae9-47c20b7eeb7a
2025-05-24 16:42:15,585 - resumatch-api - INFO - Extracted entities for resume e56b243e-49c6-4fd1-8737-2762c415fc3f
2025-05-24 16:42:15,586 - resumatch-api - INFO - Resume uploaded and processed: e56b243e-49c6-4fd1-8737-2762c415fc3f
2025-05-24 16:42:23,498 - resumatch-api - INFO - Extracted entities for resume 078eb70a-4fae-4139-bd05-af3ece03d0df
2025-05-24 16:42:23,498 - resumatch-api - INFO - Resume uploaded and processed: 078eb70a-4fae-4139-bd05-af3ece03d0df
2025-05-24 16:43:12,007 - resumatch-api - INFO - Extracted entities for resume ab93c9a2-1db8-46a7-a5d7-c1986bad68f5
2025-05-24 16:43:12,008 - resumatch-api - INFO - Resume uploaded and processed: ab93c9a2-1db8-46a7-a5d7-c1986bad68f5
2025-05-24 16:44:23,125 - resumatch-api - INFO - Extracted entities for resume 6ad4fc45-cb24-4835-a13c-f4dc9dd48356
2025-05-24 16:44:23,127 - resumatch-api - INFO - Resume uploaded and processed: 6ad4fc45-cb24-4835-a13c-f4dc9dd48356
2025-05-24 16:50:18,186 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:50:18,186 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:50:18,186 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:50:18,186 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:50:18,186 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:50:18,205 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
2025-05-24 16:50:44,158 - resumatch-api - INFO - Job embedding computed for job 30a8f8ab-84d2-4381-848e-ca3fd606e1a2
2025-05-24 16:50:44,159 - resumatch-api - INFO - Job processed and stored: 30a8f8ab-84d2-4381-848e-ca3fd606e1a2
2025-05-24 16:50:44,161 - resumatch-api - INFO - Job embedding computed for job a49eeec7-b23b-44a7-9e75-551ef065563f
2025-05-24 16:50:44,161 - resumatch-api - INFO - Job processed and stored: a49eeec7-b23b-44a7-9e75-551ef065563f
2025-05-24 16:50:44,163 - resumatch-api - INFO - Job embedding computed for job bd6e2971-664d-478c-9e7c-4eefd36812ef
2025-05-24 16:50:44,163 - resumatch-api - INFO - Job processed and stored: bd6e2971-664d-478c-9e7c-4eefd36812ef
2025-05-24 16:50:44,165 - resumatch-api - INFO - Job embedding computed for job b55808c0-fdea-43f9-9440-d9fddb4a439b
2025-05-24 16:50:44,165 - resumatch-api - INFO - Job processed and stored: b55808c0-fdea-43f9-9440-d9fddb4a439b
2025-05-24 16:50:44,167 - resumatch-api - INFO - Job embedding computed for job e33d6d01-7a09-41d7-a205-da752756b535
2025-05-24 16:50:44,167 - resumatch-api - INFO - Job processed and stored: e33d6d01-7a09-41d7-a205-da752756b535
2025-05-24 16:50:44,168 - resumatch-api - INFO - Job embedding computed for job 77edb4d7-b113-4e83-b4c3-8b636fe2d779
2025-05-24 16:50:44,168 - resumatch-api - INFO - Job processed and stored: 77edb4d7-b113-4e83-b4c3-8b636fe2d779
2025-05-24 16:50:44,169 - resumatch-api - INFO - Job embedding computed for job 8f46449a-b389-489d-ac14-1cef13397005
2025-05-24 16:50:44,169 - resumatch-api - INFO - Job processed and stored: 8f46449a-b389-489d-ac14-1cef13397005
2025-05-24 16:50:44,171 - resumatch-api - INFO - Job embedding computed for job 67f3c6eb-0605-496c-9854-0d1f1ee2e0b1
2025-05-24 16:50:44,171 - resumatch-api - INFO - Job processed and stored: 67f3c6eb-0605-496c-9854-0d1f1ee2e0b1
2025-05-24 16:50:44,172 - resumatch-api - INFO - Job embedding computed for job cc07651a-539f-43b9-aa7b-707403ba974b
2025-05-24 16:50:44,173 - resumatch-api - INFO - Job processed and stored: cc07651a-539f-43b9-aa7b-707403ba974b
2025-05-24 16:50:44,174 - resumatch-api - INFO - Job embedding computed for job 8b88fd92-efd0-4675-8872-93ddb0084a14
2025-05-24 16:50:44,174 - resumatch-api - INFO - Job processed and stored: 8b88fd92-efd0-4675-8872-93ddb0084a14
2025-05-24 16:50:44,176 - resumatch-api - INFO - Job embedding computed for job 65778059-3e73-45ad-932d-b5c294032b05
2025-05-24 16:50:44,176 - resumatch-api - INFO - Job processed and stored: 65778059-3e73-45ad-932d-b5c294032b05
2025-05-24 16:50:44,178 - resumatch-api - INFO - Job embedding computed for job a1d841b7-435e-48d8-afab-c0c56da6abe7
2025-05-24 16:50:44,178 - resumatch-api - INFO - Job processed and stored: a1d841b7-435e-48d8-afab-c0c56da6abe7
2025-05-24 16:50:44,180 - resumatch-api - INFO - Job embedding computed for job 4056094e-3598-498d-bc76-0a0843edb575
2025-05-24 16:50:44,180 - resumatch-api - INFO - Job processed and stored: 4056094e-3598-498d-bc76-0a0843edb575
2025-05-24 16:50:44,182 - resumatch-api - INFO - Job embedding computed for job 4db862d1-f39e-45b8-ab8d-a19b45134210
2025-05-24 16:50:44,182 - resumatch-api - INFO - Job processed and stored: 4db862d1-f39e-45b8-ab8d-a19b45134210
2025-05-24 16:50:44,184 - resumatch-api - INFO - Job embedding computed for job dd5cf4ab-07ad-4cb8-8d8a-50e906b466b7
2025-05-24 16:50:44,184 - resumatch-api - INFO - Job processed and stored: dd5cf4ab-07ad-4cb8-8d8a-50e906b466b7
2025-05-24 16:50:44,185 - resumatch-api - INFO - Job embedding computed for job cd21b7ff-a29e-48e9-b004-f3173dab9ec2
2025-05-24 16:50:44,185 - resumatch-api - INFO - Job processed and stored: cd21b7ff-a29e-48e9-b004-f3173dab9ec2
2025-05-24 16:50:44,187 - resumatch-api - INFO - Job embedding computed for job 73f2ba59-ac99-4553-b491-223d3e534b31
2025-05-24 16:50:44,187 - resumatch-api - INFO - Job processed and stored: 73f2ba59-ac99-4553-b491-223d3e534b31
2025-05-24 16:50:49,830 - resumatch-api - INFO - Extracted entities for resume ef2b41cf-8db6-4a42-ab9b-a97d36ec988d
2025-05-24 16:50:49,831 - resumatch-api - INFO - Resume uploaded and processed: ef2b41cf-8db6-4a42-ab9b-a97d36ec988d
2025-05-24 16:51:00,451 - resumatch-api - INFO - Extracted entities for resume 284f6b04-cae7-49c0-b3e2-0b69fed4ff43
2025-05-24 16:51:00,452 - resumatch-api - INFO - Resume uploaded and processed: 284f6b04-cae7-49c0-b3e2-0b69fed4ff43
2025-05-24 16:52:51,406 - resumatch-api - INFO - Initializing API components...
2025-05-24 16:52:51,406 - resumatch-api - INFO - Document processor initialized
2025-05-24 16:52:51,406 - resumatch-api - INFO - Entity extractor initialized
2025-05-24 16:52:51,406 - resumatch-api - INFO - Text embedder initialized with model: simple-tfidf
2025-05-24 16:52:51,406 - resumatch-api - INFO - Embedding dimension: 300
2025-05-24 16:52:51,411 - resumatch-api - INFO - Loaded pre-trained model from: /Users/<USER>/Desktop/projects/ResuMatch/resumatch/api/../models/siamese_model.pth using legacy model architecture
